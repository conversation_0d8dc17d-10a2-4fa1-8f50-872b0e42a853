#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import argparse
from typing import Dict, List

def load_multiline_json_objects(content: str) -> List[Dict]:
    """
    加载多行JSON对象格式的数据（如expand_0716_100.jsonl的格式）
    
    Args:
        content: 文件内容字符串
        
    Returns:
        List[Dict]: JSON数据列表
    """
    objects = []
    current_obj = ''
    brace_count = 0
    in_string = False
    escape_next = False
    
    for char in content:
        current_obj += char
        
        if escape_next:
            escape_next = False
            continue
            
        if char == '\\':
            escape_next = True
            continue
            
        if char == '"' and not escape_next:
            in_string = not in_string
            continue
            
        if not in_string:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    # 完整的JSON对象
                    try:
                        obj = json.loads(current_obj.strip())
                        objects.append(obj)
                        current_obj = ''
                    except json.JSONDecodeError as e:
                        print(f"JSON对象解析失败: {e}")
                        current_obj = ''
    
    print(f"成功加载 {len(objects)} 个JSON对象")
    return objects

def load_jsonl_data(file_handle) -> List[Dict]:
    """
    加载JSONL格式数据（每行一个JSON对象）
    
    Args:
        file_handle: 文件句柄
        
    Returns:
        List[Dict]: JSON数据列表
    """
    data = []
    for line_num, line in enumerate(file_handle, 1):
        line = line.strip()
        if not line:
            continue
        try:
            obj = json.loads(line)
            data.append(obj)
        except json.JSONDecodeError as e:
            print(f"第{line_num}行JSON解析失败: {e}")
    
    print(f"成功加载 {len(data)} 个JSONL对象")
    return data

def detect_file_format(file_path: str) -> str:
    """
    检测文件格式（JSON 或 JSONL）
    
    Args:
        file_path: 文件路径
        
    Returns:
        str: 'json' 或 'jsonl'
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            if not first_line:
                return 'json'
                
            # 尝试解析第一行
            try:
                json.loads(first_line)
                # 如果第一行是有效JSON，检查是否还有更多行
                second_line = f.readline().strip()
                if second_line and second_line.startswith('{'):
                    return 'jsonl'
                else:
                    # 重新读取整个文件检查是否是单个JSON对象
                    f.seek(0)
                    content = f.read().strip()
                    try:
                        json.loads(content)
                        return 'json'
                    except:
                        return 'jsonl'  # 可能是多行JSON对象格式
            except json.JSONDecodeError:
                return 'json'  # 假设是标准JSON格式
    except Exception as e:
        print(f"检测文件格式时出错: {e}")
        return 'json'

def load_json_data(file_path: str) -> List[Dict]:
    """
    加载JSON或JSONL文件数据，支持多种格式
    
    Args:
        file_path: JSON/JSONL文件路径
        
    Returns:
        List[Dict]: JSON数据列表
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return []
    
    file_format = detect_file_format(file_path)
    print(f"检测到文件格式: {file_format}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_format == 'json':
                # 尝试标准JSON格式
                try:
                    data = json.load(f)
                    if isinstance(data, dict):
                        return [data]  # 单个对象转为列表
                    elif isinstance(data, list):
                        return data
                    else:
                        print(f"未知的JSON数据类型: {type(data)}")
                        return []
                except json.JSONDecodeError:
                    # 如果标准JSON解析失败，尝试多行JSON对象格式
                    print("标准JSON解析失败，尝试多行JSON对象格式")
                    f.seek(0)
                    return load_multiline_json_objects(f.read())
            else:
                # JSONL格式：每行一个JSON对象
                return load_jsonl_data(f)
                
    except Exception as e:
        print(f"读取文件失败: {e}")
        return []

def has_all_required_fields(record: Dict) -> bool:
    """
    检查记录是否包含所有必需的字段
    
    Args:
        record: JSON记录
        
    Returns:
        bool: 是否包含所有必需字段
    """
    required_fields = [
        'StrengthsAndResources',
        'SocialSupportSystem', 
        'FormativeExperiences',
        'InterestsAndValues'
    ]
    
    for field in required_fields:
        if field not in record:
            return False
        
        # 检查字段是否为空
        value = record[field]
        if not value:  # None, 空列表, 空字典, 空字符串
            return False
            
        # 对于列表类型，检查是否有内容
        if isinstance(value, list) and len(value) == 0:
            return False
            
        # 对于字典类型，检查是否有内容
        if isinstance(value, dict) and len(value) == 0:
            return False
    
    return True

def filter_complete_records(input_file: str, output_file: str, max_records: int = 200):
    """
    筛选包含所有必需字段的记录
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        max_records: 最大记录数
    """
    print(f"开始处理文件: {input_file}")
    
    # 加载数据
    data = load_json_data(input_file)
    if not data:
        print("未能加载任何数据")
        return
    
    print(f"总共加载了 {len(data)} 条记录")
    
    # 筛选完整记录
    complete_records = []
    for i, record in enumerate(data):
        if has_all_required_fields(record):
            complete_records.append(record)
            print(f"找到第 {len(complete_records)} 条完整记录 (原始索引: {i})")
            
            if len(complete_records) >= max_records:
                break
    
    print(f"\n筛选完成！找到 {len(complete_records)} 条包含所有必需字段的记录")
    
    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(complete_records, f, ensure_ascii=False, indent=2)
    
    print(f"结果已保存到: {output_file}")
    
    # 统计信息
    print(f"\n统计信息:")
    print(f"- 输入记录总数: {len(data)}")
    print(f"- 完整记录数量: {len(complete_records)}")
    print(f"- 完整率: {len(complete_records)/len(data)*100:.2f}%")

def main():
    parser = argparse.ArgumentParser(description='筛选包含所有必需字段的JSON记录')
    parser.add_argument('input_file', help='输入的JSON/JSONL文件路径')
    parser.add_argument('-o', '--output', default='filtered_complete_records.json', 
                       help='输出文件路径 (默认: filtered_complete_records.json)')
    parser.add_argument('-n', '--max-records', type=int, default=200,
                       help='最大记录数 (默认: 200)')
    
    args = parser.parse_args()
    
    filter_complete_records(args.input_file, args.output, args.max_records)

if __name__ == "__main__":
    main()
