#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版JSONL转JSON转换器
专门处理连续JSON对象格式的文件
"""

import json
import sys
from pathlib import Path


def parse_continuous_json(content):
    """
    解析连续的JSON对象（没有换行分隔的格式）
    """
    json_objects = []
    brace_count = 0
    start_pos = 0
    
    for i, char in enumerate(content):
        if char == '{':
            if brace_count == 0:
                start_pos = i
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                # 找到一个完整的JSON对象
                json_str = content[start_pos:i+1]
                try:
                    obj = json.loads(json_str)
                    json_objects.append(obj)
                except json.JSONDecodeError as e:
                    print(f"警告: 解析JSON对象时出错: {e}")
                    print(f"位置: {start_pos}-{i+1}")
                    print(f"内容预览: {json_str[:200]}...")
    
    return json_objects


def convert_file(input_file, output_file=None):
    """
    转换单个文件
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"错误: 文件不存在 - {input_file}")
        return False
    
    # 生成输出文件名
    if output_file is None:
        output_file = input_path.stem + '_c.json'
    
    print(f"正在处理: {input_file}")
    
    try:
        # 读取文件
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 解析JSON对象
        json_objects = parse_continuous_json(content)
        
        if not json_objects:
            print(f"错误: 在文件 {input_file} 中未找到有效的JSON对象")
            return False
        
        print(f"找到 {len(json_objects)} 个JSON对象")
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_objects, f, ensure_ascii=False, indent=2)
        
        print(f"转换完成: {output_file}")
        return True
        
    except Exception as e:
        print(f"错误: 处理文件 {input_file} 时出现异常: {e}")
        return False


def main():
    """
    主函数 - 处理命令行参数
    """
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_converter.py <输入文件> [输出文件]")
        print("")
        print("示例:")
        print("  python simple_converter.py expand_0716_100.jsonl")
        print("  python simple_converter.py expand_0716_100.jsonl output.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    success = convert_file(input_file, output_file)
    
    if success:
        print("转换成功!")
    else:
        print("转换失败!")
        sys.exit(1)


if __name__ == '__main__':
    main()
