# 智能标记关键词脚本使用说明

## 功能概述

这个脚本用于分析JSON数据中各字段的值分布，并智能标记概率过高的条目以实现数据平衡。主要功能包括：

1. **统计各字段值的概率分布**（以百分比形式显示）
2. **识别概率过高的值**
3. **智能标记策略**：随机选择恰当数量的高频值条目进行标记
4. **数据平衡目标**：删除标记条目后，剩余数据分布趋向平均
5. **支持选择性字段分析和标记**

## 支持的字段

默认分析以下字段：
- Gender（性别）
- Age（年龄）
- Occupation（职业）
- Topic（主题）
- Subtopic（子主题）
- Personality（性格特征）
- Emotional Experience Words（情感体验词汇）
- Core Drive（核心驱动）
- Reaction Pattern（反应模式）

## 使用方法

### 1. 仅查看统计信息

```bash
python scripts/randomly_label_keywords.py expand_0716_100.json --stats-only
```

### 2. 智能标记（推荐）

```bash
# 使用默认阈值（1.5倍平均概率）
python scripts/randomly_label_keywords.py expand_0716_100.json --label -o output.json

# 自定义阈值
python scripts/randomly_label_keywords.py expand_0716_100.json --label -t 2.0 -o output.json
```

### 3. 选择性字段分析和标记

```bash
# 只分析Gender、Age、Occupation字段，只对Gender和Age进行标记
python scripts/randomly_label_keywords.py expand_0716_100.json \
  --fields Gender Age Occupation \
  --label \
  --label-fields Gender Age \
  -o output.json
```

## 参数说明

- `input_file`: 输入JSON文件路径（必需）
- `-o, --output`: 输出文件路径（可选，不指定则不保存）
- `-f, --fields`: 要分析的字段列表（可选，默认为预设字段）
- `-t, --threshold`: 概率阈值倍数（可选，默认1.5）
- `--label`: 启用智能标记功能
- `--label-fields`: 要标记的字段列表（可选，默认为所有分析字段）
- `--stats-only`: 仅显示统计信息，不进行标记

## 输出格式

标记后的数据会在每个条目中添加两个字段：

- `Label`: 布尔值，表示该条目是否被标记
- `Label_keys`: 字符串列表，包含有问题的字段名称

### 示例输出

```json
{
  "Gender": "女",
  "Age": 16,
  "Occupation": "高中生",
  "Topic": "学业压力",
  "Subtopic": "压力管理",
  "Personality": ["焦虑型", "完美主义"],
  "Emotional Experience Words": ["焦虑", "困惑", "无助"],
  "Core Drive": "价值追求者-失败恐惧",
  "Reaction Pattern": "直接求助与迷茫探索型",
  "Label": true,
  "Label_keys": ["Gender", "Age", "Occupation", "Reaction Pattern"]
}
```

## 智能标记逻辑

### 核心算法

1. **概率分析**：计算每个字段中各值的出现概率
2. **阈值设定**：计算平均概率（1/唯一值数量）和阈值（平均概率 × 阈值倍数）
3. **超量计算**：对于超过阈值的高频值，计算需要删除的数量
   - 目标数量 = 总条目数 × 平均概率
   - 超量数量 = 当前数量 - 目标数量
4. **随机标记**：在包含高频值的条目中随机选择恰好的数量进行标记
5. **平衡目标**：删除标记条目后，剩余数据分布趋向平均

### 示例说明

假设总量100条，某字段有10个唯一值：
- 平均概率: 10.00%
- 阈值设定: 15.00% (1.5倍)
- 某值出现: 41.00% (41次)
- 目标数量: 100 × 10% = 10条
- 需标记: 41 - 10 = 31条
- 结果: 删除31条后剩余10条，达到平均水平

## 注意事项

1. 对于列表类型的字段（如Personality），会统计每个元素的出现次数
2. 如果原数据已有Label和Label_keys字段，脚本会智能更新而不是覆盖
3. 标记是累积的，多次运行会保留之前的标记结果
4. 建议先使用`--stats-only`查看数据分布，再决定合适的阈值

## 实际应用场景

1. **数据质量检查**: 识别数据中过度集中的值，以百分比形式直观显示
2. **数据平衡**: 智能标记需要减少的高频值条目，实现分布均匀化
3. **采样策略**: 为数据采样提供科学的标记依据
4. **机器学习预处理**: 平衡训练数据，避免模型偏向高频类别
5. **统计分析**: 确保各类别样本数量相对均衡，提高分析可靠性

## 输出示例

### 统计信息显示
```
【Reaction Pattern】
  总计数: 100
  唯一值数量: 10
  平均概率: 10.00%
  概率最高的值:
    直接求助与迷茫探索型: 41.00% (41次)
    关系探索者: 16.00% (16次)
    情绪倾诉者: 15.00% (15次)
```

### 标记分析过程
```
【Reaction Pattern】分析:
  平均概率: 10.00%
  阈值: 15.00%
  直接求助与迷茫探索型: 41.00% (41次) -> 需标记 31 条
  关系探索者: 16.00% (16次) -> 需标记 6 条
```
