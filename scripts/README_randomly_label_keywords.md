# 随机标记关键词脚本使用说明

## 功能概述

这个脚本用于分析JSON数据中各字段的值分布，并标记概率过高的条目。主要功能包括：

1. **统计各字段值的概率分布**
2. **识别概率过高的值**
3. **为数据条目添加标签**
4. **支持随机标记模式**
5. **支持选择性字段分析和标记**

## 支持的字段

默认分析以下字段：
- Gender（性别）
- Age（年龄）
- Occupation（职业）
- Topic（主题）
- Subtopic（子主题）
- Personality（性格特征）
- Emotional Experience Words（情感体验词汇）
- Core Drive（核心驱动）
- Reaction Pattern（反应模式）

## 使用方法

### 1. 仅查看统计信息

```bash
python scripts/randomly_label_keywords.py expand_0716_100.json --stats-only
```

### 2. 基于概率的标记

```bash
# 使用默认阈值（1.5倍平均概率）
python scripts/randomly_label_keywords.py expand_0716_100.json --label -o output.json

# 自定义阈值
python scripts/randomly_label_keywords.py expand_0716_100.json --label -t 2.0 -o output.json
```

### 3. 随机标记

```bash
# 随机标记30%的条目
python scripts/randomly_label_keywords.py expand_0716_100.json --random-label 0.3 -o output.json
```

### 4. 选择性字段分析和标记

```bash
# 只分析Gender、Age、Occupation字段，只对Gender和Age进行标记
python scripts/randomly_label_keywords.py expand_0716_100.json \
  --fields Gender Age Occupation \
  --label \
  --label-fields Gender Age \
  -o output.json
```

## 参数说明

- `input_file`: 输入JSON文件路径（必需）
- `-o, --output`: 输出文件路径（可选，不指定则不保存）
- `-f, --fields`: 要分析的字段列表（可选，默认为预设字段）
- `-t, --threshold`: 概率阈值倍数（可选，默认1.5）
- `--label`: 启用基于概率的标记功能
- `--label-fields`: 要标记的字段列表（可选，默认为所有分析字段）
- `--random-label`: 随机标记模式，指定标记比例（0-1之间）
- `--stats-only`: 仅显示统计信息，不进行标记

## 输出格式

标记后的数据会在每个条目中添加两个字段：

- `Label`: 布尔值，表示该条目是否被标记
- `Label_keys`: 字符串列表，包含有问题的字段名称

### 示例输出

```json
{
  "Gender": "女",
  "Age": 16,
  "Occupation": "高中生",
  "Topic": "学业压力",
  "Subtopic": "压力管理",
  "Personality": ["焦虑型", "完美主义"],
  "Emotional Experience Words": ["焦虑", "困惑", "无助"],
  "Core Drive": "价值追求者-失败恐惧",
  "Reaction Pattern": "直接求助与迷茫探索型",
  "Label": true,
  "Label_keys": ["Gender", "Age", "Occupation", "Reaction Pattern"]
}
```

## 标记逻辑

### 基于概率的标记

1. 计算每个字段中各值的出现概率
2. 计算平均概率（1/唯一值数量）
3. 设定阈值（平均概率 × 阈值倍数）
4. 标记包含超过阈值概率值的条目

### 随机标记

1. 根据指定比例随机选择条目
2. 为选中的条目随机分配1-3个问题字段

## 注意事项

1. 对于列表类型的字段（如Personality），会统计每个元素的出现次数
2. 如果原数据已有Label和Label_keys字段，脚本会智能更新而不是覆盖
3. 标记是累积的，多次运行会保留之前的标记结果
4. 建议先使用`--stats-only`查看数据分布，再决定合适的阈值

## 实际应用场景

1. **数据质量检查**: 识别数据中过度集中的值
2. **数据平衡**: 标记需要减少的高频值条目
3. **采样策略**: 为数据采样提供标记依据
4. **A/B测试**: 随机标记条目用于对比实验
