#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
随机标记关键词脚本
用于分析JSON数据中各字段的值分布，并标记概率过高的条目
"""

import json
import random
import argparse
from collections import defaultdict, Counter
from typing import Dict, List, Any, Set, Tuple
import copy


class KeywordAnalyzer:
    """关键词分析器"""

    def __init__(self, target_fields: List[str] = None):
        """
        初始化分析器

        Args:
            target_fields: 要分析的字段列表，如果为None则使用默认字段
        """
        self.default_fields = [
            "Gender", "Age", "Occupation", "Topic", "Subtopic",
            "Personality", "Emotional Experience Words", "Core Drive", "Reaction Pattern"
        ]
        self.target_fields = target_fields or self.default_fields
        self.field_stats = {}
        self.data = []

    def load_data(self, file_path: str) -> None:
        """加载JSON数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            print(f"成功加载 {len(self.data)} 条数据")
        except Exception as e:
            print(f"加载数据失败: {e}")
            raise

    def analyze_field_distribution(self) -> Dict[str, Dict]:
        """分析各字段的值分布"""
        self.field_stats = {}

        for field in self.target_fields:
            field_counter = Counter()
            total_count = 0

            for item in self.data:
                if field not in item:
                    continue

                value = item[field]

                # 处理不同类型的值
                if isinstance(value, list):
                    # 对于列表类型，统计每个元素
                    for v in value:
                        field_counter[str(v)] += 1
                        total_count += 1
                elif isinstance(value, (str, int, float)):
                    # 对于字符串和数字类型
                    field_counter[str(value)] += 1
                    total_count += 1
                else:
                    # 其他类型转为字符串
                    field_counter[str(value)] += 1
                    total_count += 1

            # 计算概率
            probabilities = {}
            for val, count in field_counter.items():
                probabilities[val] = count / total_count if total_count > 0 else 0

            self.field_stats[field] = {
                'counter': field_counter,
                'total_count': total_count,
                'probabilities': probabilities,
                'unique_values': len(field_counter)
            }

        return self.field_stats

    def print_statistics(self) -> None:
        """打印统计信息"""
        print("\n" + "="*80)
        print("字段分布统计")
        print("="*80)

        for field, stats in self.field_stats.items():
            print(f"\n【{field}】")
            print(f"  总计数: {stats['total_count']}")
            print(f"  唯一值数量: {stats['unique_values']}")
            print(f"  平均概率: {1/stats['unique_values']:.4f}")

            # 显示概率最高的前5个值
            sorted_probs = sorted(stats['probabilities'].items(),
                                key=lambda x: x[1], reverse=True)
            print("  概率最高的值:")
            for val, prob in sorted_probs[:5]:
                print(f"    {val}: {prob:.4f} ({stats['counter'][val]}次)")

    def identify_high_probability_items(self, threshold_multiplier: float = 1.5) -> Dict[str, Set[str]]:
        """
        识别概率过高的值

        Args:
            threshold_multiplier: 阈值倍数，超过平均概率的多少倍算作过高

        Returns:
            字段名 -> 过高概率值集合的字典
        """
        high_prob_values = {}

        for field, stats in self.field_stats.items():
            if stats['unique_values'] == 0:
                continue

            avg_prob = 1 / stats['unique_values']
            threshold = avg_prob * threshold_multiplier

            high_values = set()
            for val, prob in stats['probabilities'].items():
                if prob > threshold:
                    high_values.add(val)

            high_prob_values[field] = high_values

            if high_values:
                print(f"\n{field} 中概率过高的值 (阈值: {threshold:.4f}):")
                for val in high_values:
                    prob = stats['probabilities'][val]
                    count = stats['counter'][val]
                    print(f"  {val}: {prob:.4f} ({count}次)")

        return high_prob_values

    def label_items(self, high_prob_values: Dict[str, Set[str]],
                   selected_fields: List[str] = None) -> List[Dict]:
        """
        为数据项添加标签

        Args:
            high_prob_values: 各字段中概率过高的值
            selected_fields: 选择要标记的字段，如果为None则使用所有字段

        Returns:
            添加了标签的数据列表
        """
        if selected_fields is None:
            selected_fields = self.target_fields

        labeled_data = copy.deepcopy(self.data)
        label_stats = {"total": len(labeled_data), "labeled": 0}

        for item in labeled_data:
            should_label = False
            problem_fields = []

            # 检查每个选定的字段
            for field in selected_fields:
                if field not in item or field not in high_prob_values:
                    continue

                value = item[field]
                high_values = high_prob_values[field]

                # 检查该字段是否包含概率过高的值
                if isinstance(value, list):
                    # 对于列表类型，检查是否有任何元素在高概率值中
                    for v in value:
                        if str(v) in high_values:
                            should_label = True
                            if field not in problem_fields:
                                problem_fields.append(field)
                            break
                elif isinstance(value, (str, int, float)):
                    # 对于字符串和数字类型
                    if str(value) in high_values:
                        should_label = True
                        problem_fields.append(field)
                else:
                    # 其他类型
                    if str(value) in high_values:
                        should_label = True
                        problem_fields.append(field)

            # 更新或添加标签字段
            if "Label" in item:
                # 如果已有Label字段，更新为True（如果需要标记）或保持原值
                item["Label"] = item["Label"] or should_label
            else:
                item["Label"] = should_label

            # 更新或添加Label_keys字段
            if "Label_keys" in item:
                # 如果已有Label_keys字段，添加新的问题字段
                existing_keys = set(item["Label_keys"])
                existing_keys.update(problem_fields)
                item["Label_keys"] = list(existing_keys)
            else:
                item["Label_keys"] = problem_fields

            if should_label:
                label_stats["labeled"] += 1

        print(f"\n标记统计:")
        print(f"  总条目数: {label_stats['total']}")
        print(f"  被标记条目数: {label_stats['labeled']}")
        print(f"  标记比例: {label_stats['labeled']/label_stats['total']:.2%}")

        return labeled_data

    def save_labeled_data(self, labeled_data: List[Dict], output_path: str) -> None:
        """保存标记后的数据"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(labeled_data, f, ensure_ascii=False, indent=2)
            print(f"标记后的数据已保存到: {output_path}")
        except Exception as e:
            print(f"保存数据失败: {e}")
            raise

    def random_label_selection(self, target_ratio: float = 0.5,
                             selected_fields: List[str] = None) -> List[Dict]:
        """
        随机选择条目进行标记，以达到目标比例

        Args:
            target_ratio: 目标标记比例
            selected_fields: 选择要考虑的字段

        Returns:
            随机标记后的数据列表
        """
        if selected_fields is None:
            selected_fields = self.target_fields

        labeled_data = copy.deepcopy(self.data)
        total_items = len(labeled_data)
        target_count = int(total_items * target_ratio)

        # 随机选择要标记的索引
        indices_to_label = set(random.sample(range(total_items), target_count))

        for i, item in enumerate(labeled_data):
            should_label = i in indices_to_label

            # 如果被选中标记，随机选择一些字段作为问题字段
            problem_fields = []
            if should_label:
                # 随机选择1-3个字段作为问题字段
                available_fields = [f for f in selected_fields if f in item]
                if available_fields:
                    num_problem_fields = random.randint(1, min(3, len(available_fields)))
                    problem_fields = random.sample(available_fields, num_problem_fields)

            # 更新标签字段
            if "Label" in item:
                item["Label"] = item["Label"] or should_label
            else:
                item["Label"] = should_label

            # 更新Label_keys字段
            if "Label_keys" in item:
                existing_keys = set(item["Label_keys"])
                existing_keys.update(problem_fields)
                item["Label_keys"] = list(existing_keys)
            else:
                item["Label_keys"] = problem_fields

        print(f"\n随机标记统计:")
        print(f"  总条目数: {total_items}")
        print(f"  目标标记数: {target_count}")
        print(f"  实际标记数: {len(indices_to_label)}")
        print(f"  标记比例: {len(indices_to_label)/total_items:.2%}")

        return labeled_data


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析JSON数据中各字段的值分布并标记概率过高的条目')
    parser.add_argument('input_file', help='输入JSON文件路径')
    parser.add_argument('-o', '--output', help='输出文件路径（如果不指定则不保存）')
    parser.add_argument('-f', '--fields', nargs='+',
                       help='要分析的字段列表（默认为预设字段）')
    parser.add_argument('-t', '--threshold', type=float, default=1.5,
                       help='概率阈值倍数（默认1.5倍平均概率）')
    parser.add_argument('--label', action='store_true',
                       help='启用标记功能')
    parser.add_argument('--label-fields', nargs='+',
                       help='要标记的字段列表（默认为所有分析字段）')
    parser.add_argument('--random-label', type=float,
                       help='随机标记模式，指定标记比例（0-1之间）')
    parser.add_argument('--stats-only', action='store_true',
                       help='仅显示统计信息，不进行标记')

    args = parser.parse_args()

    # 创建分析器
    analyzer = KeywordAnalyzer(target_fields=args.fields)

    # 加载数据
    print(f"正在加载数据: {args.input_file}")
    analyzer.load_data(args.input_file)

    # 分析字段分布
    print("正在分析字段分布...")
    analyzer.analyze_field_distribution()

    # 显示统计信息
    analyzer.print_statistics()

    if args.stats_only:
        print("\n仅显示统计信息模式，程序结束。")
        return

    labeled_data = None

    if args.random_label is not None:
        # 随机标记模式
        if not (0 <= args.random_label <= 1):
            print("错误: 随机标记比例必须在0-1之间")
            return

        print(f"\n使用随机标记模式，标记比例: {args.random_label:.2%}")
        labeled_data = analyzer.random_label_selection(
            target_ratio=args.random_label,
            selected_fields=args.label_fields
        )

    elif args.label:
        # 基于概率的标记模式
        print(f"\n识别概率过高的值（阈值倍数: {args.threshold}）...")
        high_prob_values = analyzer.identify_high_probability_items(args.threshold)

        if any(high_prob_values.values()):
            print("\n开始标记条目...")
            labeled_data = analyzer.label_items(
                high_prob_values=high_prob_values,
                selected_fields=args.label_fields
            )
        else:
            print("没有发现概率过高的值，无需标记。")

    # 保存结果
    if labeled_data and args.output:
        analyzer.save_labeled_data(labeled_data, args.output)
    elif labeled_data:
        print("\n注意: 未指定输出文件，标记后的数据未保存。")

    print("\n程序执行完成。")


if __name__ == "__main__":
    main()