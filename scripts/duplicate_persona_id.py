#!/usr/bin/env python3
"""
检测JSON/JSONL文件中persona_id字段值相同的条目
支持.json和.jsonl格式文件
"""

import json
import argparse
import sys
from collections import defaultdict, Counter
import os


def load_data(file_path):
    """
    加载JSON或JSONL文件数据
    支持标准JSONL格式和多行JSON对象格式
    :param file_path: 文件路径
    :return: 数据列表
    """
    data = []
    file_ext = os.path.splitext(file_path)[1].lower()

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_ext == '.json':
                # JSON格式：整个文件是一个JSON数组
                data = json.load(f)
                if not isinstance(data, list):
                    print(f"错误：JSON文件应该包含一个数组，但得到了 {type(data)}")
                    return []
            elif file_ext == '.jsonl':
                # JSONL格式：支持标准JSONL和多行JSON对象格式
                content = f.read().strip()

                # 尝试标准JSONL格式（每行一个JSON对象）
                lines = content.split('\n')
                if all(line.strip().startswith('{') and line.strip().endswith('}') for line in lines if line.strip()):
                    # 标准JSONL格式
                    for line_num, line in enumerate(lines, 1):
                        line = line.strip()
                        if line:  # 跳过空行
                            try:
                                item = json.loads(line)
                                data.append(item)
                            except json.JSONDecodeError as e:
                                print(f"警告：第{line_num}行JSON解析失败: {e}")
                                continue
                else:
                    # 多行JSON对象格式，尝试按空行分割或连续的}{ 分割
                    # 先尝试按 }\n{ 模式分割
                    json_objects = []
                    current_obj = ""
                    brace_count = 0

                    for char in content:
                        current_obj += char
                        if char == '{':
                            brace_count += 1
                        elif char == '}':
                            brace_count -= 1
                            if brace_count == 0:
                                # 完整的JSON对象
                                json_objects.append(current_obj.strip())
                                current_obj = ""

                    # 解析每个JSON对象
                    for obj_num, json_str in enumerate(json_objects, 1):
                        if json_str:
                            try:
                                item = json.loads(json_str)
                                data.append(item)
                            except json.JSONDecodeError as e:
                                print(f"警告：第{obj_num}个JSON对象解析失败: {e}")
                                print(f"对象内容: {json_str[:200]}...")
                                continue
            else:
                print(f"错误：不支持的文件格式 {file_ext}，仅支持 .json 和 .jsonl")
                return []

    except FileNotFoundError:
        print(f"错误：文件不存在 {file_path}")
        return []
    except Exception as e:
        print(f"错误：读取文件失败 {file_path}: {e}")
        return []

    return data


def check_duplicate_persona_ids(data):
    """
    检查重复的persona_id
    :param data: 数据列表
    :return: (重复统计字典, 总条目数, 有persona_id的条目数)
    """
    persona_id_count = Counter()
    persona_id_positions = defaultdict(list)  # 记录每个persona_id出现的位置
    total_items = len(data)
    valid_items = 0
    
    for idx, item in enumerate(data):
        if not isinstance(item, dict):
            print(f"警告：第{idx+1}个条目不是字典格式，跳过")
            continue
            
        if 'persona_id' in item:
            persona_id = item['persona_id']
            persona_id_count[persona_id] += 1
            persona_id_positions[persona_id].append(idx + 1)  # 使用1基索引
            valid_items += 1
        else:
            print(f"警告：第{idx+1}个条目缺少persona_id字段")
    
    # 找出重复的persona_id
    duplicates = {pid: count for pid, count in persona_id_count.items() if count > 1}
    
    return duplicates, persona_id_positions, total_items, valid_items


def print_duplicate_report(duplicates, persona_id_positions, total_items, valid_items, show_positions=False):
    """
    打印重复检测报告
    :param duplicates: 重复的persona_id字典
    :param persona_id_positions: persona_id位置字典
    :param total_items: 总条目数
    :param valid_items: 有效条目数
    :param show_positions: 是否显示重复条目的位置
    """
    print(f"\n=== 重复检测报告 ===")
    print(f"总条目数: {total_items}")
    print(f"包含persona_id字段的条目数: {valid_items}")
    print(f"唯一persona_id数量: {len(set(persona_id_positions.keys()))}")
    
    if duplicates:
        print(f"\n发现 {len(duplicates)} 个重复的persona_id:")
        print("-" * 60)
        
        for persona_id, count in sorted(duplicates.items()):
            print(f"persona_id: '{persona_id}' - 出现 {count} 次", end="")
            if show_positions:
                positions = persona_id_positions[persona_id]
                print(f" (位置: {', '.join(map(str, positions))})")
            else:
                print()
        
        # 统计重复条目总数
        duplicate_items_count = sum(duplicates.values()) - len(duplicates)
        print(f"\n重复条目总数: {duplicate_items_count}")
        print(f"去重后条目数: {valid_items - duplicate_items_count}")
    else:
        print(f"\n✅ 未发现重复的persona_id")


def save_duplicate_report(duplicates, persona_id_positions, output_file):
    """
    保存重复检测报告到文件
    :param duplicates: 重复的persona_id字典
    :param persona_id_positions: persona_id位置字典
    :param output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("重复persona_id检测报告\n")
            f.write("=" * 50 + "\n\n")
            
            if duplicates:
                for persona_id, count in sorted(duplicates.items()):
                    positions = persona_id_positions[persona_id]
                    f.write(f"persona_id: {persona_id}\n")
                    f.write(f"  出现次数: {count}\n")
                    f.write(f"  位置: {', '.join(map(str, positions))}\n\n")
            else:
                f.write("未发现重复的persona_id\n")
        
        print(f"\n报告已保存到: {output_file}")
    except Exception as e:
        print(f"保存报告失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='检测JSON/JSONL文件中重复的persona_id')
    parser.add_argument('file', help='输入文件路径 (.json 或 .jsonl)')
    parser.add_argument('--show-positions', '-p', action='store_true', 
                       help='显示重复条目的具体位置')
    parser.add_argument('--output', '-o', help='保存报告到指定文件')
    parser.add_argument('--quiet', '-q', action='store_true', 
                       help='静默模式，只显示是否有重复')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误：文件不存在 {args.file}")
        sys.exit(1)
    
    # 加载数据
    print(f"正在读取文件: {args.file}")
    data = load_data(args.file)
    
    if not data:
        print("错误：未能加载任何数据")
        sys.exit(1)
    
    # 检查重复
    duplicates, persona_id_positions, total_items, valid_items = check_duplicate_persona_ids(data)
    
    if args.quiet:
        # 静默模式
        if duplicates:
            print(f"发现重复: {len(duplicates)} 个重复的persona_id")
            sys.exit(1)
        else:
            print("未发现重复")
            sys.exit(0)
    else:
        # 详细模式
        print_duplicate_report(duplicates, persona_id_positions, total_items, valid_items, args.show_positions)
    
    # 保存报告
    if args.output:
        save_duplicate_report(duplicates, persona_id_positions, args.output)
    
    # 根据是否有重复设置退出码
    sys.exit(1 if duplicates else 0)


if __name__ == "__main__":
    main()
