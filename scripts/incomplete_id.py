import json
import sys
import os

OUTPUT_FILE = 'incomplete_in_400.txt'

def load_data(file_path):
    """
    加载JSON文件数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"错误: 文件不存在 - {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式错误 - {e}")
        return None
    except Exception as e:
        print(f"错误: 读取文件时出现异常 - {e}")
        return None

def is_complete(record):
    """
    检查记录是否包含所有必需字段
    """
    required_fields = [
        'StrengthsAndResources',
        'SocialSupportSystem', 
        'FormativeExperiences',
        'InterestsAndValues'
    ]
    
    for field in required_fields:
        if field not in record:
            return False
    return True

def find_incomplete_records(data):
    """
    找出缺失字段的记录，返回persona_id列表
    """
    incomplete_ids = []
    
    if isinstance(data, list):
        # 如果是数组格式
        for i, record in enumerate(data):
            if not isinstance(record, dict):
                print(f"警告: 第{i+1}条记录不是字典格式")
                continue
                
            if not is_complete(record):
                persona_id = record.get('persona_id', f'未知ID_第{i+1}条记录')
                incomplete_ids.append(persona_id)
                
                # 显示缺失的字段
                missing_fields = []
                required_fields = ['StrengthsAndResources', 'SocialSupportSystem', 'FormativeExperiences', 'InterestsAndValues']
                for field in required_fields:
                    if field not in record:
                        missing_fields.append(field)
                print(f"记录 {persona_id} 缺失字段: {', '.join(missing_fields)}")
    
    elif isinstance(data, dict):
        # 如果是单个对象
        if not is_complete(data):
            persona_id = data.get('persona_id', '未知ID')
            incomplete_ids.append(persona_id)
            
            missing_fields = []
            required_fields = ['StrengthsAndResources', 'SocialSupportSystem', 'FormativeExperiences', 'InterestsAndValues']
            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
            print(f"记录 {persona_id} 缺失字段: {', '.join(missing_fields)}")
    
    else:
        print("错误: 数据格式不正确，应该是JSON数组或对象")
        return []
    
    return incomplete_ids

def save_incomplete_ids(incomplete_ids, output_file):
    """
    将不完整记录的ID保存到文件
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for persona_id in incomplete_ids:
                f.write(f"{persona_id}\n")
        print(f"已将 {len(incomplete_ids)} 个不完整记录的ID保存到: {output_file}")
    except Exception as e:
        print(f"错误: 保存文件时出现异常 - {e}")

def main():
    if len(sys.argv) < 2:
        print("用法: python incomplete_id.py <JSON文件路径> [输出文件路径]")
        print("示例: python incomplete_id.py personas.json")
        print("示例: python incomplete_id.py personas.json incomplete_ids.txt")
        sys.exit(1)
        
    file_path = sys.argv[1]
    
    # 生成输出文件名
    output_file = OUTPUT_FILE
    
    # 加载数据
    data = load_data(file_path)
    if data is None:
        sys.exit(1)
    
    # 查找不完整的记录
    incomplete_ids = find_incomplete_records(data)
    
    if incomplete_ids:
        # 保存结果
        save_incomplete_ids(incomplete_ids, output_file)
        print(f"\n总计发现 {len(incomplete_ids)} 个不完整的记录")
    else:
        print("所有记录都包含必需的字段!")

if __name__ == "__main__":
    main()
