import json
import sys
from typing import Dict, List, Any

def load_json_file(filepath: str) -> List[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {filepath}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"错误: {filepath} 不是有效的JSON文件")
        sys.exit(1)

def save_json_file(filepath: str, data: List[Dict[str, Any]]) -> None:
    """保存JSON文件"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"成功保存到 {filepath}")
    except Exception as e:
        print(f"错误: 保存文件时出错 - {e}")
        sys.exit(1)

def update_json_by_persona_id(input_file: str, output_file: str) -> None:
    """根据persona_id更新JSON文件"""
    # 加载输入和输出文件
    input_data = load_json_file(input_file)
    output_data = load_json_file(output_file)
    
    # 将输出数据转换为以persona_id为键的字典
    output_dict = {item.get('persona_id'): item for item in output_data}
    
    # 统计更新数量
    updated_count = 0
    
    # 遍历输入数据并更新
    for input_item in input_data:
        persona_id = input_item.get('persona_id')
        
        if persona_id is None:
            print(f"警告: 输入数据中发现没有persona_id的条目，跳过")
            continue
            
        if persona_id in output_dict:
            # 更新对应的条目
            output_dict[persona_id] = input_item
            updated_count += 1
            print(f"已更新 persona_id: {persona_id}")
        else:
            print(f"警告: 在输出文件中未找到 persona_id: {persona_id}")
    
    # 将字典转换回列表
    updated_output_data = list(output_dict.values())
    
    # 保存更新后的数据
    save_json_file(output_file, updated_output_data)
    print(f"更新完成! 共更新了 {updated_count} 个条目")

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python update.py <输入文件路径> <输出文件路径>")
        print("示例: python update.py input.json output.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    print(f"开始更新...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    
    update_json_by_persona_id(input_file, output_file)

if __name__ == "__main__":
    main()
