#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计JSON文件中指定字段的值包含特定内容的情况
"""

import json
import argparse
import sys
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter

# 默认配置
JSON_FILE = "deprecated_data/expand_0725_200.json"
KEYWORD = "恐惧"
TOP_N = 10  # 显示包含个数最多的前N项


def count_keyword_in_field(field_value: Any, keyword: str) -> int:
    """统计单个字段值中包含关键词的次数"""
    count = 0

    if isinstance(field_value, str):
        # 字符串类型：直接统计
        count += field_value.count(keyword)

    elif isinstance(field_value, list):
        # 列表类型：遍历每个元素
        for item in field_value:
            if isinstance(item, str):
                count += item.count(keyword)
            elif isinstance(item, dict):
                # 列表中的字典元素，转换为JSON字符串统计
                item_str = json.dumps(item, ensure_ascii=False)
                count += item_str.count(keyword)

    elif isinstance(field_value, dict):
        # 字典类型：遍历所有键值对
        for key, value in field_value.items():
            # 统计键中的关键词
            if isinstance(key, str):
                count += key.count(keyword)
            # 统计值中的关键词
            if isinstance(value, str):
                count += value.count(keyword)
            elif isinstance(value, list):
                for sub_item in value:
                    if isinstance(sub_item, str):
                        count += sub_item.count(keyword)

    elif isinstance(field_value, (int, float)):
        # 数值类型：转换为字符串统计
        count += str(field_value).count(keyword)

    return count


def analyze_json_data(data: List[Dict], keyword: str, target_fields: List[str] = None) -> Dict[str, List[Tuple[str, int]]]:
    """分析JSON数据，统计每个字段中包含关键词的情况"""

    # 如果没有指定字段，则分析所有字段（除了persona_id）
    if not target_fields:
        # 获取所有可能的字段名
        all_fields = set()
        for item in data:
            all_fields.update(item.keys())
        target_fields = [field for field in all_fields if field != 'persona_id']

    # 存储每个字段的统计结果：{字段名: [(persona_id, 包含次数), ...]}
    field_stats = defaultdict(list)

    for item in data:
        persona_id = item.get('persona_id', 'unknown')

        for field_name in target_fields:
            if field_name in item:
                field_value = item[field_name]
                keyword_count = count_keyword_in_field(field_value, keyword)

                if keyword_count > 0:
                    field_stats[field_name].append((persona_id, keyword_count))

    # 对每个字段的结果按包含次数降序排序
    for field_name in field_stats:
        field_stats[field_name].sort(key=lambda x: x[1], reverse=True)

    return dict(field_stats)


def print_statistics(field_stats: Dict[str, List[Tuple[str, int]]], keyword: str, top_n: int):
    """打印统计结果"""
    print(f"关键词 '{keyword}' 在各字段中的统计结果:")
    print("=" * 80)

    if not field_stats:
        print("没有找到包含该关键词的字段。")
        return

    # 按字段名排序输出
    for field_name in sorted(field_stats.keys()):
        results = field_stats[field_name]
        total_personas = len(results)
        total_occurrences = sum(count for _, count in results)

        print(f"\n字段: {field_name}")
        print(f"包含关键词的样本数: {total_personas}")
        print(f"关键词总出现次数: {total_occurrences}")

        if results:
            print(f"前 {min(top_n, len(results))} 个包含最多的样本:")
            for i, (persona_id, count) in enumerate(results[:top_n], 1):
                print(f"  {i:2d}. {persona_id}: {count} 次")

        print("-" * 60)

    # 总体统计
    all_results = []
    for results in field_stats.values():
        all_results.extend(results)

    if all_results:
        # 按persona_id合并统计
        persona_totals = defaultdict(int)
        for persona_id, count in all_results:
            persona_totals[persona_id] += count

        # 排序
        sorted_personas = sorted(persona_totals.items(), key=lambda x: x[1], reverse=True)

        print(f"\n总体统计 - 包含关键词最多的前 {min(top_n, len(sorted_personas))} 个样本:")
        for i, (persona_id, total_count) in enumerate(sorted_personas[:top_n], 1):
            print(f"  {i:2d}. {persona_id}: {total_count} 次")


def main():
    parser = argparse.ArgumentParser(description='统计JSON文件中指定字段包含特定内容的情况')
    parser.add_argument('json_file', nargs='?', default=JSON_FILE,
                       help=f'输入的JSON文件路径 (默认: {JSON_FILE})')
    parser.add_argument('keyword', nargs='?', default=KEYWORD,
                       help=f'要搜索的关键词 (默认: {KEYWORD})')
    parser.add_argument('--fields', '-f', nargs='*',
                       help='指定要统计的字段名，不指定则统计所有字段')
    parser.add_argument('--top', '-t', type=int, default=TOP_N,
                       help=f'显示包含个数最多的前N项 (默认: {TOP_N})')

    args = parser.parse_args()

    # 显示使用的参数
    print(f"使用参数:")
    print(f"  JSON文件: {args.json_file}")
    print(f"  关键词: {args.keyword}")
    print(f"  指定字段: {args.fields if args.fields else '全部字段'}")
    print(f"  显示前N项: {args.top}")
    print()

    # 读取JSON文件
    try:
        with open(args.json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误：找不到文件 {args.json_file}")
        sys.exit(1)
    except json.JSONDecodeError:
        print(f"错误：{args.json_file} 不是有效的JSON文件")
        sys.exit(1)

    if not isinstance(data, list):
        print("错误：JSON文件应该包含一个对象数组")
        sys.exit(1)

    # 分析数据
    field_stats = analyze_json_data(data, args.keyword, args.fields)

    # 打印统计结果
    print_statistics(field_stats, args.keyword, args.top)


if __name__ == "__main__":
    main()