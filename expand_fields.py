from openai import OpenAI
import json
import random
import os
import datetime
import argparse
import sys
import time
from tqdm import tqdm
import concurrent.futures  # 新增并行处理库
import threading  # 新增线程安全支持
from typing import Dict, Any, List, Tuple

from prompts import *

# 模型配置：{模型名: 权重}
MODELS = {
    "doubao-seed-1-6-250615": 0.75,
    "deepseek-r1-250528": 0.25
}

# Prompt配置：{prompt变量名: 权重}
PROMPTS = {
    expand_prompt: 0.6,
    expand_prompt_sim: 0.3,
    expand_prompt_meta: 0.1
}

INPUT_FILE = "test_personas.json"  # 改为测试文件
OUTPUT_FILE = "expand_0726_1.jsonl"
MAX_RETRIES = 10
FAILED_FILE = "error_personas.txt"
# "deepseek-v3-250324""gpt-4.1","doubao-seed-1-6-250615""deepseek-r1-250528","claude-sonnet-4-20250514" "doubao-1-5-pro-32k-character-250228"
# parser.add_argument('n', type=int, nargs='?', help='选择要处理的角色卡片数量（在默认模式下必需）')
# parser.add_argument('--error', default=FAILED_FILE, help='错误文件名，默认为error_personas.txt')
# parser.add_argument('--workers', type=int, default=5, help='并行工作线程数，默认为5')
# parser.add_argument('--max_retries', type=int, default=MAX_RETRIES, help='处理失败时的最大重试次数，默认为3')
# parser.add_argument('-m', '--mode', choices=['txt'], help='处理模式：txt模式从文件读取persona_id')
# parser.add_argument('-c', '--config', help='txt模式下的配置文件路径，包含要处理的persona_id列表')


def select_random_model():
    """根据权重随机选择模型"""
    models = list(MODELS.keys())
    weights = list(MODELS.values())
    return random.choices(models, weights=weights)[0]

def select_random_prompt():
    """根据权重随机选择prompt"""
    prompts = list(PROMPTS.keys())
    weights = list(PROMPTS.values())
    return random.choices(prompts, weights=weights)[0]

def validate_expanded_fields(expanded_fields: dict, persona_id: str = "未知") -> tuple:
    """
    验证扩展字段的完整性和合法性
    :param expanded_fields: 扩展字段字典
    :param persona_id: persona_id用于错误定位
    :return: (is_valid, errors)
    """
    errors = []
    required_fields = ["StrengthsAndResources", "SocialSupportSystem", "FormativeExperiences", "InterestsAndValues"]

    # 检查必需字段是否存在
    for field in required_fields:
        if field not in expanded_fields:
            errors.append(f"缺少必需字段 '{field}'")

    # 验证各字段格式
    if "StrengthsAndResources" in expanded_fields:
        errors.extend(_validate_strengths_and_resources(expanded_fields["StrengthsAndResources"], persona_id))

    if "SocialSupportSystem" in expanded_fields:
        errors.extend(_validate_social_support_system(expanded_fields["SocialSupportSystem"], persona_id))

    if "FormativeExperiences" in expanded_fields:
        errors.extend(_validate_formative_experiences(expanded_fields["FormativeExperiences"], persona_id))

    if "InterestsAndValues" in expanded_fields:
        errors.extend(_validate_interests_and_values(expanded_fields["InterestsAndValues"], persona_id))

    return len(errors) == 0, errors

def _validate_strengths_and_resources(value: any, persona_id: str) -> list:
    """验证StrengthsAndResources格式"""
    errors = []

    if not isinstance(value, list):
        errors.append(f"StrengthsAndResources应为数组类型，实际为{type(value).__name__}")
        return errors

    if len(value) == 0:
        errors.append(f"StrengthsAndResources不能为空数组")
        return errors

    for i, item in enumerate(value):
        if not isinstance(item, str):
            errors.append(f"StrengthsAndResources[{i}]应为字符串类型")
        elif not item.strip():
            errors.append(f"StrengthsAndResources[{i}]不能为空字符串")

    return errors

def _validate_social_support_system(value: any, persona_id: str) -> list:
    """验证SocialSupportSystem格式"""
    errors = []

    if not isinstance(value, dict):
        errors.append(f"SocialSupportSystem应为对象类型，实际为{type(value).__name__}")
        return errors

    if len(value) == 0:
        errors.append(f"SocialSupportSystem不能为空对象")
        return errors

    for key, val in value.items():
        if not isinstance(key, str) or not key.strip():
            errors.append(f"SocialSupportSystem的键应为非空字符串")
        if not isinstance(val, str) or not val.strip():
            errors.append(f"SocialSupportSystem['{key}']的值应为非空字符串")

    return errors

def _validate_formative_experiences(value: any, persona_id: str) -> list:
    """验证FormativeExperiences格式"""
    errors = []

    if not isinstance(value, list):
        errors.append(f"FormativeExperiences应为数组类型，实际为{type(value).__name__}")
        return errors

    if len(value) == 0:
        errors.append(f"FormativeExperiences不能为空数组")
        return errors

    for i, item in enumerate(value):
        if not isinstance(item, dict):
            errors.append(f"FormativeExperiences[{i}]应为对象类型")
            continue

        # 检查是否包含"事件"字段
        if "事件" not in item:
            errors.append(f"FormativeExperiences[{i}]缺少'事件'字段")
        elif not isinstance(item["事件"], str) or not item["事件"].strip():
            errors.append(f"FormativeExperiences[{i}]['事件']应为非空字符串")

        # 检查是否包含"影响"字段
        if "影响" not in item:
            errors.append(f"FormativeExperiences[{i}]缺少'影响'字段")
        elif not isinstance(item["影响"], str) or not item["影响"].strip():
            errors.append(f"FormativeExperiences[{i}]['影响']应为非空字符串")

    return errors

def _validate_interests_and_values(value: any, persona_id: str) -> list:
    """验证InterestsAndValues格式"""
    errors = []

    if not isinstance(value, dict):
        errors.append(f"InterestsAndValues应为对象类型，实际为{type(value).__name__}")
        return errors

    if len(value) == 0:
        errors.append(f"InterestsAndValues不能为空对象")
        return errors

    # 检查是否包含Interests字段
    if "Interests" not in value:
        errors.append(f"InterestsAndValues缺少'Interests'字段")
    elif not isinstance(value["Interests"], list):
        errors.append(f"InterestsAndValues['Interests']应为数组类型")
    elif len(value["Interests"]) == 0:
        errors.append(f"InterestsAndValues['Interests']不能为空数组")
    else:
        # 检查Interests数组中的每个元素
        for i, interest in enumerate(value["Interests"]):
            if not isinstance(interest, str) or not interest.strip():
                errors.append(f"InterestsAndValues['Interests'][{i}]应为非空字符串")

    # 检查是否包含Values字段
    if "Values" not in value:
        errors.append(f"InterestsAndValues缺少'Values'字段")
    elif not isinstance(value["Values"], list):
        errors.append(f"InterestsAndValues['Values']应为数组类型")
    elif len(value["Values"]) == 0:
        errors.append(f"InterestsAndValues['Values']不能为空数组")
    else:
        # 检查Values数组中的每个元素
        for i, val in enumerate(value["Values"]):
            if not isinstance(val, str) or not val.strip():
                errors.append(f"InterestsAndValues['Values'][{i}]应为非空字符串")

    return errors

def get_response(user_message, system_prompt, model, temperature=1.4, token_record=None):
    """
    获取对话生成的响应
    :param user_message: 用户消息
    :param system_prompt: 系统提示
    :param model: 模型名称
    :param temperature: 温度参数
    :param token_record: token使用记录字典
    :return: 生成的响应文本
    """
    
    # # 指定url和key
    # api_key = "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"
    # base_url = "http://*************:3000/v1"
    
    base_url = "https://ark.cn-beijing.volces.com/api/v3"
    api_key = "5ac78442-9d9a-4e85-8ae5-fca8086daf43"
    
    client = OpenAI(
    base_url=base_url,
    api_key=api_key)
    
    try:
        response = client.chat.completions.create(
            messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
            model=model,
            temperature=temperature,
            response_format={
                'type': 'json_object'
            },
            stream=False
        )
        
        # 记录token使用情况
        if token_record is not None and hasattr(response, 'usage'):
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
            
            token_record['prompt_tokens'] = token_record.get('prompt_tokens', 0) + prompt_tokens
            token_record['completion_tokens'] = token_record.get('completion_tokens', 0) + completion_tokens
            token_record['total_calls'] = token_record.get('total_calls', 0) + 1
        
        return response.choices[0].message.content
    except Exception as e:
        if "429" in str(e):
            print(f"速率限制超出 (429)，等待5秒后重试...")
            time.sleep(5)
            return get_response(user_message, system_prompt, model, temperature, token_record)
        else:
            print(f"API调用错误: {e}")
            time.sleep(3)
            return get_response(user_message, system_prompt, model, temperature, token_record)


def append_to_output_file(persona_data, output_file, lock=None):
    """
    实时追加单条样例到输出文件（JSONL格式）
    :param persona_data: 单条角色卡片数据
    :param output_file: 输出文件名
    :param lock: 线程锁
    """
    if lock:
        lock.acquire()

    try:
        with open(output_file, 'a', encoding='utf-8') as f:
            json.dump(persona_data, f, ensure_ascii=False, indent=2)
            f.write('\n')
        result = True
    except Exception as e:
        print(f"保存到文件失败: {e}")
        result = False
    finally:
        if lock:
            lock.release()
    return result

def append_to_error_file(persona_id, error_file, lock=None, error_details=None):
    """
    实时追加失败的persona_id到错误文件
    :param persona_id: 失败的角色ID
    :param error_file: 错误文件名
    :param lock: 线程锁
    :param error_details: 错误详情（可选）
    """
    if lock:
        lock.acquire()

    try:
        with open(error_file, 'a', encoding='utf-8') as f:
            if error_details:
                timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"{persona_id} | {timestamp} | {'; '.join(error_details)}\n")
            else:
                f.write(f"{persona_id}\n")
        result = True
    except Exception as e:
        print(f"保存错误记录失败: {e}")
        result = False
    finally:
        if lock:
            lock.release()
    return result

# 新增：处理单个角色卡片的并行函数
def process_persona(idx, persona, output_file, error_file, lock, token_records=None, max_retries=10):
    """
    并行处理单个角色卡片的函数，失败时会重试
    :param idx: 角色在列表中的索引
    :param persona: 角色卡片数据
    :param output_file: 输出文件路径
    :param error_file: 错误文件路径
    :param lock: 线程锁
    :param token_records: 各模型token使用记录字典
    :param max_retries: 最大重试次数
    :return: (idx, success)
    """
    persona_id = persona.get('persona_id', '未知')
    retry_count = 0

    while retry_count <= max_retries:  # 允许初始尝试 + max_retries次重试
        try:
            # 随机选择模型和prompt
            selected_model = select_random_model()
            selected_prompt = select_random_prompt()

            # 将角色卡片转换为字符串作为user_message
            user_message = json.dumps(persona, ensure_ascii=False, indent=2)

            # 获取对应模型的token记录
            model_token_record = token_records.get(selected_model) if token_records else None

            # 调用get_response获取扩展字段
            response_text = get_response(user_message, selected_prompt, selected_model, token_record=model_token_record)

            # 处理可能被代码块标记包裹的内容
            if response_text.startswith("```") and response_text.endswith("```"):
                response_text = response_text.strip("`")
                # 移除可能的语言标识符(如```json)
                lines = response_text.split("\n")
                if len(lines) > 1 and not lines[0].strip():
                    response_text = "\n".join(lines[1:])
                elif len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
                    response_text = "\n".join(lines[1:])

            # 解析响应
            try:
                expanded_fields = json.loads(response_text)

                # 使用新的验证函数检查完整性和合法性
                is_valid, validation_errors = validate_expanded_fields(expanded_fields, persona_id)

                if not is_valid:
                    retry_count += 1
                    if retry_count <= max_retries:
                        wait_time = retry_count * 2  # 递增等待时间
                        print(f"\n响应验证失败, ID: {persona_id}: {validation_errors}，第{retry_count}次重试，等待{wait_time}秒...")
                        print(f"原始响应: {response_text[:500]}{'...' if len(response_text) > 500 else ''}")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"\n响应验证失败, ID: {persona_id}: {validation_errors}，已达到最大重试次数{max_retries}")
                        print(f"原始响应: {response_text[:500]}{'...' if len(response_text) > 500 else ''}")
                        # 记录失败的persona_id到错误文件，包含详细错误信息
                        append_to_error_file(persona_id, error_file, lock, validation_errors)
                        return idx, False

                # 创建扩展后的角色卡片
                expanded_persona = persona.copy()
                expanded_persona.update(expanded_fields)

                # 最终验证：确保扩展后的完整记录包含所有必需字段
                final_validation_errors = []
                all_required_fields = ["persona_id", "StrengthsAndResources", "SocialSupportSystem", "FormativeExperiences", "InterestsAndValues"]
                for field in all_required_fields:
                    if field not in expanded_persona:
                        final_validation_errors.append(f"最终记录缺少字段: {field}")

                if final_validation_errors:
                    print(f"\n最终验证失败, ID: {persona_id}: {final_validation_errors}")
                    append_to_error_file(persona_id, error_file, lock, final_validation_errors)
                    return idx, False

                # 实时写入到输出文件
                if append_to_output_file(expanded_persona, output_file, lock):
                    print(f"\n角色卡片 {idx+1}, ID: {persona_id} 处理成功")
                    return idx, True
                else:
                    print(f"\n角色卡片 {idx+1}, ID: {persona_id} 保存失败")
                    append_to_error_file(persona_id, error_file, lock, ["文件保存失败"])
                    return idx, False

            except json.JSONDecodeError as e:
                retry_count += 1
                if retry_count <= max_retries:
                    wait_time = retry_count * 2  # 递增等待时间
                    print(f"\n解析响应失败, ID: {persona_id}: {e}，第{retry_count}次重试，等待{wait_time}秒...")
                    print(f"原始响应: {response_text}")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"\n解析响应失败, ID: {persona_id}: {e}，已达到最大重试次数{max_retries}")
                    print(f"原始响应: {response_text[:500]}{'...' if len(response_text) > 500 else ''}")
                    # 记录失败的persona_id到错误文件
                    append_to_error_file(persona_id, error_file, lock, [f"JSON解析错误: {str(e)}"])
                    return idx, False

        except Exception as e:
            retry_count += 1
            if retry_count <= max_retries:
                wait_time = retry_count * 2  # 递增等待时间
                print(f"\n处理角色卡片失败, ID: {persona_id}: {e}，第{retry_count}次重试，等待{wait_time}秒...")
                time.sleep(wait_time)
                continue
            else:
                print(f"\n处理角色卡片失败, ID: {persona_id}: {e}，已达到最大重试次数{max_retries}")
                # 记录失败的persona_id到错误文件
                append_to_error_file(persona_id, error_file, lock, [f"处理异常: {str(e)}"])
                return idx, False

def read_persona_ids_from_txt(txt_file):
    """
    从txt文件中读取persona_id列表
    :param txt_file: txt文件路径
    :return: persona_id列表
    """
    try:
        with open(txt_file, 'r', encoding='utf-8') as f:
            persona_ids = []
            for line in f:
                line = line.strip()
                if line:  # 忽略空行
                    persona_ids.append(line)
        return persona_ids
    except Exception as e:
        print(f"读取txt文件失败: {e}")
        return []

def filter_personas_by_ids(personas, target_ids):
    """
    根据persona_id列表筛选角色卡片
    :param personas: 所有角色卡片列表
    :param target_ids: 目标persona_id列表
    :return: 筛选后的角色卡片列表
    """
    filtered = []
    found_ids = set()
    
    for persona in personas:
        persona_id = str(persona.get('persona_id', ''))
        if persona_id in target_ids:
            filtered.append(persona)
            found_ids.add(persona_id)
    
    # 检查是否有未找到的ID
    missing_ids = set(target_ids) - found_ids
    if missing_ids:
        print(f"警告：以下persona_id在数据中未找到: {', '.join(missing_ids)}")
    
    return filtered

def generate_validation_report(output_file: str, error_file: str, total_processed: int, successful_count: int):
    """
    生成验证报告，检查输出文件的完整性
    :param output_file: 输出文件路径
    :param error_file: 错误文件路径
    :param total_processed: 总处理数量
    :param successful_count: 成功数量
    """
    print(f"\n{'='*60}")
    print(f"📊 处理完成验证报告")
    print(f"{'='*60}")

    # 基本统计
    failed_count = total_processed - successful_count
    success_rate = (successful_count / total_processed * 100) if total_processed > 0 else 0

    print(f"总处理数量: {total_processed}")
    print(f"成功数量: {successful_count}")
    print(f"失败数量: {failed_count}")
    print(f"成功率: {success_rate:.1f}%")

    # 检查输出文件
    if os.path.exists(output_file):
        try:
            # 统计输出文件中的记录数
            with open(output_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()

            if content:
                # 计算JSON对象数量（简单方法：统计以}结尾的行数）
                json_objects = len([line for line in content.split('\n') if line.strip().endswith('}')])
                print(f"输出文件记录数: {json_objects}")

                if json_objects != successful_count:
                    print(f"⚠️  警告：输出文件记录数({json_objects})与成功数量({successful_count})不匹配")
            else:
                print(f"⚠️  警告：输出文件为空")

        except Exception as e:
            print(f"❌ 检查输出文件时出错: {e}")
    else:
        print(f"❌ 输出文件不存在: {output_file}")

    # 检查错误文件
    if os.path.exists(error_file) and failed_count > 0:
        try:
            with open(error_file, 'r', encoding='utf-8') as f:
                error_lines = [line.strip() for line in f if line.strip()]

            print(f"错误文件记录数: {len(error_lines)}")

            if len(error_lines) != failed_count:
                print(f"⚠️  警告：错误文件记录数({len(error_lines)})与失败数量({failed_count})不匹配")

            # 分析错误类型
            error_types = {}
            for line in error_lines:
                if '|' in line:
                    parts = line.split('|')
                    if len(parts) >= 3:
                        error_detail = parts[2].strip()
                        error_type = error_detail.split(':')[0] if ':' in error_detail else error_detail
                        error_types[error_type] = error_types.get(error_type, 0) + 1

            if error_types:
                print(f"\n错误类型统计:")
                for error_type, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                    print(f"  {error_type}: {count}次")

        except Exception as e:
            print(f"❌ 检查错误文件时出错: {e}")

    print(f"{'='*60}")

    # 如果成功率低于90%，给出建议
    if success_rate < 90:
        print(f"💡 建议：成功率较低({success_rate:.1f}%)，可能需要：")
        print(f"   1. 检查prompt是否清晰明确")
        print(f"   2. 增加最大重试次数")
        print(f"   3. 调整模型参数（如temperature）")
        print(f"   4. 检查输入数据质量")
    elif success_rate >= 95:
        print(f"✅ 处理质量优秀！成功率达到{success_rate:.1f}%")

    return {
        'total_processed': total_processed,
        'successful_count': successful_count,
        'failed_count': failed_count,
        'success_rate': success_rate
    }

def main():
    """
    主函数：处理命令行参数，读取personas.json，调用大模型扩展字段，并输出结果到personas_expand.json
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='扩展角色卡片字段')
    parser.add_argument('n', type=int, nargs='?', help='选择要处理的角色卡片数量（在默认模式下必需）')
    parser.add_argument('--output', default=OUTPUT_FILE, help='输出文件名，默认为personas_expand.json')
    parser.add_argument('--error', default=FAILED_FILE, help='错误文件名，默认为error_personas.txt')
    parser.add_argument('--workers', type=int, default=6, help='并行工作线程数，默认为6')
    parser.add_argument('--max_retries', type=int, default=MAX_RETRIES, help='处理失败时的最大重试次数，默认为3')
    parser.add_argument('-m', '--mode', choices=['txt'], help='处理模式：txt模式从文件读取persona_id')
    parser.add_argument('-c', '--config', help='txt模式下的配置文件路径，包含要处理的persona_id列表')
    args = parser.parse_args()

    # 验证参数组合
    if args.mode == 'txt':
        if not args.config:
            print("错误：txt模式下必须提供 -c 参数指定包含persona_id的txt文件")
            sys.exit(1)
        if not os.path.exists(args.config):
            print(f"错误：指定的txt文件不存在: {args.config}")
            sys.exit(1)
    else:
        if args.n is None:
            print("错误：默认模式下必须提供要处理的角色卡片数量 n")
            sys.exit(1)

    # 创建token记录目录
    token_record_dir = "token_record"
    if not os.path.exists(token_record_dir):
        os.makedirs(token_record_dir)

    # 为每个模型创建token记录字典
    token_records = {}
    for model in MODELS.keys():
        token_records[model] = {
            'model': model,
            'prompt_tokens': 0,
            'completion_tokens': 0,
            'total_calls': 0,
            'start_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    # 读取personas.json
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            personas = json.load(f)
    except Exception as e:
        print(f"读取personas.json失败: {e}")
        sys.exit(1)
    
    # 根据模式选择要处理的角色卡片
    if args.mode == 'txt':
        # txt模式：从文件读取persona_id列表
        target_ids = read_persona_ids_from_txt(args.config)
        if not target_ids:
            print("错误：txt文件中没有有效的persona_id")
            sys.exit(1)
        
        selected_personas = filter_personas_by_ids(personas, target_ids)
        if not selected_personas:
            print("错误：没有找到匹配的角色卡片")
            sys.exit(1)
        
        print(f"txt模式：从文件 {args.config} 读取到 {len(target_ids)} 个persona_id，匹配到 {len(selected_personas)} 个角色卡片")
    else:
        # 默认模式：按数量选择
        if args.n <= 0 or args.n > len(personas):
            print(f"n的值无效，必须在1到{len(personas)}之间")
            sys.exit(1)
        
        selected_personas = personas[:args.n]
        print(f"默认模式：选择前 {args.n} 个角色卡片进行处理")

    # 清空输出文件和错误文件
    with open(args.output, 'w', encoding='utf-8') as f:
        pass  # 清空文件

    # 初始化错误文件，添加说明头部
    with open(args.error, 'w', encoding='utf-8') as f:
        f.write(f"# 扩展字段处理错误记录\n")
        f.write(f"# 格式: persona_id | 时间戳 | 错误详情\n")
        f.write(f"# 生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"# 处理模式: {'txt模式' if args.mode == 'txt' else '默认模式'}\n")
        if args.mode == 'txt':
            f.write(f"# 配置文件: {args.config}\n")
        f.write(f"# 最大重试次数: {args.max_retries}\n")
        f.write(f"# 并行线程数: {args.workers}\n")
        f.write(f"#\n")

    # 创建线程锁以确保线程安全
    lock = threading.Lock()

    print(f"开始并行处理{len(selected_personas)}个角色卡片...")

    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=args.workers) as executor:
        # 准备任务列表
        futures = []
        for i, persona in enumerate(selected_personas):
            futures.append(
                executor.submit(
                    process_persona,
                    i,
                    persona,
                    args.output,
                    args.error,
                    lock,
                    token_records,
                    args.max_retries
                )
            )

        # 使用tqdm显示进度
        successful_count = 0
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="并行处理角色卡片"):
            try:
                _, success = future.result()
                if success:
                    successful_count += 1
            except Exception as e:
                print(f"\n处理角色卡片时发生未知错误: {e}")

        print(f"\n所有角色卡片处理完成，结果已实时写入文件: {args.output}")
        print(f"错误记录已写入文件: {args.error}")

        # 生成验证报告
        generate_validation_report(args.output, args.error, len(selected_personas), successful_count)

        # 可选：运行详细的格式验证（如果存在验证脚本）
        validation_script = "scripts/invalid_detect.py"
        if os.path.exists(validation_script) and successful_count > 0:
            print(f"\n🔍 运行详细格式验证...")
            try:
                import subprocess
                result = subprocess.run([
                    sys.executable, validation_script, args.output
                ], capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    print(f"✅ 格式验证通过")
                else:
                    print(f"⚠️  格式验证发现问题，详细信息请查看验证脚本输出")
                    if result.stdout:
                        print("验证输出:")
                        print(result.stdout[-1000:])  # 只显示最后1000个字符

            except subprocess.TimeoutExpired:
                print(f"⚠️  格式验证超时")
            except Exception as e:
                print(f"⚠️  运行格式验证时出错: {e}")
    
    # 记录结束时间并保存token使用记录
    end_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # 为每个模型添加结束时间和统计信息
    for model, record in token_records.items():
        record['end_time'] = end_time
        record['successful_personas'] = successful_count
        record['total_personas'] = len(selected_personas)

    # 保存各模型的token记录到文件
    token_record_file = os.path.join(token_record_dir, f"token_record_{timestamp}.json")
    try:
        with open(token_record_file, 'w', encoding='utf-8') as f:
            json.dump(token_records, f, ensure_ascii=False, indent=2)
        print(f"\nToken使用记录已保存到: {token_record_file}")

        # 打印各模型的token使用统计
        total_prompt_tokens = sum(record['prompt_tokens'] for record in token_records.values())
        total_completion_tokens = sum(record['completion_tokens'] for record in token_records.values())
        print(f"总输入tokens: {total_prompt_tokens}, 总输出tokens: {total_completion_tokens}")

        for model, record in token_records.items():
            if record['total_calls'] > 0:
                print(f"  {model}: 调用{record['total_calls']}次, 输入{record['prompt_tokens']}, 输出{record['completion_tokens']}")
    except Exception as e:
        print(f"\n保存Token记录失败: {e}")

    print(f"\n成功完成！共处理 {len(selected_personas)} 个角色卡片，成功 {successful_count} 个")

if __name__ == "__main__":
    main()