# 模拟用户角色生成器

本项目是一个高级、可配置的模拟用户角色（Persona）生成器，旨在利用大型语言模型（LLM）的强大能力，自动化创建大量多样化、真实且具有内在逻辑一致性的心理咨询场景角色卡。

## 核心设计哲学

本项目的核心是**三维正交生成**模型，它通过组合三个基本维度来构建角色，以实现最大的多样性和深度：

1.  **核心驱动 (Core Drive)**: 定义角色的**内在动机**和深层恐惧，是角色的"灵魂"。
2.  **困扰情境 (Situation)**: 定义角色面临的外部挑战和具体问题。
3.  **反应模式 (Reaction Pattern)**: 定义角色在压力下的**外在行为**和沟通方式。

通过将这三个维度进行程序化组合，并辅以**策略性增强配置**（用于确保稀有但关键的"边缘案例"被覆盖），本工具能够系统性地生成一个丰富且均衡的角色数据集。

## 主要特性

-   **多模式生成**: 内置三种独立的配置模式，以适应不同场景的需求：
    -   `k12`: 专注于 K12 阶段（中小学）学生的心理问题。
    -   `high`: 专注于高等教育阶段（大学）学生的心理问题。
    -   `universal`: 适用于更广泛的通用心理咨询场景。
-   **高度可配置与可扩展**:
    -   所有核心元素（核心驱动、反应模式）均在外部 `.yaml` 文件中定义，易于理解、修改和扩展。
    -   通过简单的文件操作，即可添加新的驱动、模式，甚至全新的生成模式。
-   **深度角色构建**:
    -   系统指令（Prompt）经过精心设计，引导 LLM 为每个角色创造独特的背景故事，解释其核心驱动的来源，确保角色有"历史感"和"灵魂"。
    -   集成**五大人格模型 (Big Five)**，要求 LLM 推断并指定角色的性格特质，增加了角色的科学性和维度。
-   **逻辑自洽保证**: `.yaml` 文件支持 `constraints` (约束) 字段，允许您定义特定驱动或模式只能与特定的职业或话题组合，从源头上保证了生成角色的合理性。
-   **高性能并发**: 利用多线程 (`ThreadPoolExecutor`) 并行处理对 LLM API 的请求，能够高效地大规模生成角色画像。
-   **结构化输出**: 所有生成的角色卡都以标准化的 JSON 格式输出，字段清晰，便于下游应用直接使用。

## 项目结构

```
Simulator/
├── archetypes.py                   # 核心生成脚本
|
├── universal_core_drives.yaml      # "universal" 模式的核心驱动定义
├── universal_reaction_patterns.yaml# "universal" 模式的反应模式定义
├── universal_system_prompt.txt     # "universal" 模式的LLM指令模板
|
├── high_core_drives.yaml           # "high" 模式的定义文件...
├── high_reaction_patterns.yaml
├── high_system_prompt.txt
|
├── core_drives.yaml                # "k12" 模式的定义文件...
├── reaction_patterns.yaml
├── system_prompt.txt
|
├── tools/
│   ├── llm_utils.py                # LLM API 调用工具
│   └── ...                         # 其他辅助脚本
|
├── requirements.txt                # 项目依赖
├── .env.example                    # 环境变量配置示例 
└── README.md                       # 本文档
```

## 快速上手

### 1. 环境准备

-   确保您已安装 Python 3.8 或更高版本。
-   克隆本项目到本地。

### 2. 安装依赖

在项目根目录下，运行以下命令安装所有必需的库：

```bash
pip install -r requirements.txt
```

### 3. 环境配置

1.  在项目根目录下，创建一个名为 `.env` 的文件。
2.  复制以下内容到 `.env` 文件中，并根据您的配置进行修改：

    ```ini
    # --- LLM Provider Settings ---
    # 可选项: "openai" 或 "gemini"
    LLM_PROVIDER="openai"

    # --- API Keys ---
    # 填入您选择的 LLM 服务商的 API Key
    OPENAI_API_KEY="sk-..."
    GEMINI_API_KEY="..."

    # --- Generation Mode ---
    # 选择生成模式，这将决定使用哪一套 .yaml 和 .txt 配置文件
    # 可选项: "k12", "high", "universal"
    ARCHETYPE_MODE="universal"

    # --- Performance Settings ---
    # 并发请求数，请根据您的机器性能和API速率限制进行调整
    MAX_WORKERS=64
    ```

### 4. （可选）自定义配置

-   **修改生成元素**: 打开对应模式的 `_core_drives.yaml` 或 `_reaction_patterns.yaml` 文件，您可以直接修改、添加或删除其中的条目来影响生成结果。
-   **调整 LLM 指令**: 编辑对应模式的 `_system_prompt.txt` 文件可以改变 LLM 生成故事的风格、深度和格式。

### 5. 运行生成器

1.  打开 `archetypes.py` 脚本。
2.  在脚本底部的 `if __name__ == "__main__":` 代码块中，根据您的需求配置 `TOTAL_PROFILES_TO_GENERATE`（要生成的总数）和 `STRATEGIC_CONFIGS`（策略性增强配置）。
3.  执行脚本：

    ```bash
    python archetypes.py
    ```

### 6. 查看结果

脚本运行结束后，生成的角色画像将保存在项目根目录下，文件名类似于 `generated_character_profiles_[mode]_[count].json`。

## 依赖项

所有依赖项均已在 `requirements.txt` 文件中列出：

-   `PyYAML`
-   `python-dotenv`
-   `tqdm`
-   `openai`
-   `google-generativeai` 