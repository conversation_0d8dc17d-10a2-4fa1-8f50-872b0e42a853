- name: "直接求助与迷茫探索型"
  description: "用户遇到了具体的生活困扰（如工作不顺、婚姻危机、育儿难题），目的明确，就是想找到能解决问题的具体方法。语气可能焦虑、烦躁或无助，并反复追问“我该怎么办？”。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "情绪倾诉者"
  description: "用户的主要动机是宣泄强烈的情绪（悲伤、愤怒、委屈、痛苦），并不急于寻找解决方案，而是需要一个安全、不被评判的空间来表达感受。对共情和支持性话语的反应非常积极。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "绝望求死与危机边缘型"
  description: "用户正承受着来自生活多方面的巨大压力，感到孤立无援、痛苦至极，并产生了强烈的自我伤害或自杀念头。语言中充满绝望，并可能对常规帮助表现出抗拒。"
  constraints:
    allowed_topics: ["情绪困扰", "心理创伤"]
    allowed_occupations: ["所有人群"]

- name: "创伤叙事与细节描述型"
  description: "用户亲身经历过严重负面事件（如事故、暴力、重大丧失），通过详细、有逻辑地复述整个事件，来处理内心的创伤和情绪。叙事完整、细节丰富，并伴随强烈的情绪和身体感受描述。"
  constraints:
    allowed_topics: ["心理创伤", "情绪困扰"]
    allowed_occupations: ["所有人群"]

- name: "关系探索者"
  description: "用户的困扰核心围绕着某段重要的人际关系（恋爱、婚姻、亲情、友情、同事关系）。他们因对方的行为感到困惑、受伤，并试图通过对话理清关系中的问题。充满不确定性和自我怀疑。"
  constraints:
    allowed_topics: ["婚恋情感", "人际关系", "亲子教育", "职场发展"]
    allowed_occupations: ["所有人群"]

- name: "习惯改变者"
  description: "用户的核心困扰是一种他们明知有害但又难以摆脱的行为习惯（如拖延、熬夜、酗酒、暴食、沉迷网络等）。他们对问题有清晰认知，并因此感到自责和挫败，寻求打破恶性循环的具体策略。"
  constraints:
    allowed_topics: ["个人成长", "情绪困扰", "职场发展", "个人财务"]
    allowed_occupations: ["所有人群"]

- name: "迷茫回避与碎片化表达型"
  description: "用户内心有所顾虑或不信任，表达非常谨慎、模糊、碎片化。可能使用“不知道”、“没什么”、“还行”来回避核心，需要提问者不断猜测和引导才能深入。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "指责抱怨型"
  description: "用户倾向于将自己的困境归咎于外部环境或他人（如“都怪我老板/伴侣/父母”）。对话中充满抱怨、不满和委屈，当被引导向内看时会产生抗拒，并坚持是外界的错。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "充满矛盾的自我认知者"
  description: "用户内心充满矛盾。可能一方面极度自信或自恋，另一方面又在现实的某些领域感到无力、挫败。在两种截然不同的自我认知间摇摆，理想与现实脱节。"
  constraints:
    allowed_topics: ["个人成长", "婚恋情感", "职场发展"]
    allowed_occupations: ["所有人群"]

- name: "深度思辨的哲学家"
  description: "用户对生命、道德、公平等抽象和终极问题充满探索欲。对话目的不是解决个人烦恼，而是进行思想辩论，构建或验证自己的价值体系。提问宏大且逻辑性强。"
  constraints:
    allowed_topics: ["个人成长", "互动与探索"]
    allowed_occupations: ["所有人群"]

- name: "务实的信息寻求者"
  description: "用户将AI视为一个信息和知识的来源，而非情感伙伴。目标非常具体，如进行课题调研、了解某种疗法、查询某项政策等。沟通高效、直接，情绪卷入度低。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "边界试探与身份确认型"
  description: "用户对AI本身充满好奇，主要目的是为了测试AI的身份、能力和反应边界。可能会反复询问关于AI的背景信息，或说一些挑衅的话来观察其反应。"
  constraints:
    allowed_topics: ["互动与探索"]
    allowed_occupations: ["所有人群"]

- name: "躯体化倾诉者"
  description: "用户不直接表达情绪困扰，而是通过持续抱怨各种身体不适（如“胸闷”、“头痛”、“睡不着”、“没力气”、“胃不舒服”）来传递心理压力。当被问及情绪时，他们可能会回避，并坚持认为问题是纯生理的。"
  constraints:
    allowed_topics: ["情绪困扰", "个人成长"]
    allowed_occupations: ["所有人群"]

- name: "理智化分析型"
  description: "用户用一种抽离、客观、学术化的方式来讨论自己的问题，仿佛在分析一个与己无关的案例。他们大量使用专业术语或逻辑框架，以避免接触和体验真实的情感。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "犬儒式抱怨者"
  description: "用户对改变的可能性持有一种深刻的、习得性的无助感。他们会抱怨现状，但同时又以一种“看透一切”的犬儒姿态拒绝所有建议，认为“没用的”、“世界就是这样”。核心是为自己的不作为寻求合理化。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]

- name: "沉默对抗型"
  description: "用户用沉默、极简回答（嗯、哦、还行）或不相关的简短回复来应对提问。这可能源于不信任、疲惫、抗拒或无法表达。通过消极的方式来表达对抗。"
  constraints:
    allowed_topics: ["所有话题"]
    allowed_occupations: ["所有人群"]