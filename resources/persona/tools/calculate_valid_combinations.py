# calculate_valid_combinations.py
# -*- coding: utf-8 -*-
"""
这个脚本用于计算在应用所有约束之后，
archetypes.py 理论上可以生成的有效角色画像组合的总数。
支持通过命令行参数选择 K12 模式或高校模式。
"""
import yaml
from pathlib import Path
import os # Import os for path manipulation
import argparse # 新增

# --- 从 archetypes.py 复制的常量和函数 ---
# 确保路径相对于此脚本的父目录 (项目根目录)
SCRIPT_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = SCRIPT_DIR.parent

# --- K12 模式常量 ---
TOPIC_SUBTOPIC_MAP = {
    "学业与成长": ["学业压力", "考试焦虑", "成绩困扰", "学习方法", "学习动力", "厌学情绪", "注意力不集中", "作业问题", "升学规划", "时间管理"],
    "人际与社交": ["同学关系", "交友困惑", "校园霸凌", "被孤立", "沟通技巧", "师生关系", "网络交友", "人际边界", "信任问题", "社交恐惧"],
    "家庭与亲子": ["亲子沟通", "家庭压力", "父母期望", "家庭矛盾", "家庭暴力", "缺乏理解", "隐私空间"],
    "情绪与内在状态": ["创伤事件复述与应激", "抑郁低落与绝望", "焦虑烦躁与失控", "孤独感与无价值感", "自伤与自杀念头", "冲动与愤怒管理", "网络/手机成瘾", "躯体化症状"],
    "自我认知与价值观": ["自信心与自我价值", "外貌焦虑", "性格困扰", "理想与现实冲突", "个人成长哲学", "生命意义与道德思辨", "青春期萌动"],
    "互动模式与对话探索": ["AI身份试探", "测试系统边界", "幽默/游戏式互动", "哲学式辩论", "碎片化/回避式表达", "寻求角色扮演"],
    "家长视角与求助": ["孩子社交问题", "孩子情绪行为异常", "孩子学业求助", "亲子沟通方法", "如何引导与帮助孩子", "担忧孩子网络使用"]
}
OCCUPATIONS_LIST = ["小学生", "初中生", "高中生", "老师", "家长"]

# --- 新增: 高校模式的常量 ---
HIGH_TOPIC_SUBTOPIC_MAP = {
    "学业与成长": ["学业压力", "考试焦虑", "成绩困扰", "学习方法", "学习习惯", "厌学情绪", "升学规划", "作业问题", "时间管理"],
    "人际与社交": ["同学关系", "交友困惑", "校园霸凌", "人际边界", "师生关系", "网络交友"],
    "恋爱与情感": ["择偶标准", "亲密关系", "恋爱困扰", "分手失恋", "情感表达", "恋爱观念", "暗恋/单恋"],
    "职业与规划": ["就业焦虑", "职业发展", "实习困扰", "职场人际", "工作压力", "职业规划"],
    "家庭与亲子": ["亲子沟通", "家庭压力", "家庭矛盾", "家庭暴力"],
    "情绪与内在状态": ["行为习惯", "焦虑烦躁与失控", "抑郁低落与绝望", "孤独感与无价值感", "自伤与自杀念头", "冲动与愤怒管理", "网络/手机成瘾", "创伤事件复述与应激"],
    "自我认知与价值观": ["自信心与自我价值", "外貌焦虑", "性格困扰", "生命意义与道德思辨"]
}
HIGH_OCCUPATIONS_LIST = ["小学生", "初中生", "高中生", "大学生", "老师", "家长", "职场人士"]

# --- 新增: Universal 模式常量 ---
UNIVERSAL_TOPIC_SUBTOPIC_MAP = {
    "个人成长": [
        "自我探索与认知", "自信心提升", "个人价值感", "原生家庭影响", 
        "情绪管理", "压力应对", "时间管理", "习惯养成", "性格完善", "人生意义探索"
    ],
    "情绪困扰": [
        "抑郁情绪", "焦虑情绪", "恐惧情绪", "强迫现象", "双相情感障碍", 
        "疑病现象", "进食障碍", "愤怒与冲动管理", "孤独感", "成瘾行为"
    ],
    "人际关系": [
        "社交恐惧", "沟通技巧", "人际边界设定", "依赖共生", "分离个体化", 
        "讨好型人格", "家庭关系", "职场关系", "师生关系", "网络人际"
    ],
    "婚恋情感": [
        "脱单与吸引力", "情感挽回", "失恋修复", "婚姻经营", "亲密关系提升", 
        "出轨创伤", "分手与离婚抉择", "情感虐待识别", "性与亲密"
    ],
    "亲子教育": [
        "亲子沟通", "儿童青少年心理", "学业压力与厌学", "行为问题矫正", 
        "青春期议题", "家庭教育方式", "隔代教育", "网络沉迷引导"
    ],
    "职场发展": [
        "职业规划与选择", "职场减压", "人际沟通与办公室政治", "晋升瓶颈", 
        "职业倦怠", "工作生活平衡", "求职与面试", "能力提升"
    ],
    "心理创伤": [
        "童年创伤", "校园霸凌", "灾难事件应激", "情感虐待", 
        "丧失与哀悼", "意外事故后遗症", "家庭暴力"
    ],
    "性心理": [
        "性心理认知", "性少数群体(LGBTQ+)", "性功能障碍", "性癖好与特殊偏好", 
        "性压抑与羞耻感", "性与关系"
    ],
    "个人财务": [
        "财务压力", "理财规划", "消费习惯与观念", "债务问题"
    ],
    "互动与探索": [
        "AI身份试探", "测试系统边界", "角色扮演", "哲学思辨", "寻求具体信息"
    ]
}

UNIVERSAL_OCCUPATIONS_LIST = [
    # 学生阶段
    "小学生", "初中生", "高中生", "大学生", "研究生",
    # 职场角色
    "职场人士", "职场新人", "中层管理者", "高层管理者", "创业者", 
    "自由职业者", "技术人员", "蓝领工人", "艺术家",
    # 教育与医护
    "老师", "医护人员", "心理咨询师", "社会工作者",
    # 家庭角色
    "家长", "全职父母", "需要照顾长辈的成年子女",
    # 其他社会身份
    "退休人员", "失业/待业者", "长期病患", "经历重大丧失者"
]

def load_yaml_file(file_path: Path, required_keys: list):
    # Adjust file_path to be relative to the project root
    full_path = PROJECT_ROOT / file_path
    if not full_path.exists():
        raise FileNotFoundError(f"Required YAML file not found: {full_path}")
    try:
        with open(full_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        if not isinstance(data, list):
            raise ValueError(f"Invalid format in {full_path}. Expected a list of dictionaries.")
        for i, item in enumerate(data):
            if not all(key in item for key in required_keys):
                raise ValueError(f"Item at index {i} in '{full_path}' is missing one of required keys: {required_keys}.")
            item["constraints"] = item.get("constraints", {}) # Ensure constraints key exists
        print(f"Successfully loaded {len(data)} items from '{full_path}'.")
        return data
    except (yaml.YAMLError, ValueError) as e:
        print(f"Error processing YAML file '{full_path}': {e}")
        raise

def is_combination_valid(occupation: str, topic: str, config_item: dict) -> bool:
    """通用约束检查函数，增加了对'所有话题'和'所有人群'通配符的支持。"""
    constraints = config_item.get("constraints", {})
    if not isinstance(constraints, dict): return True  # No constraints means valid

    # Occupation check
    if (allowed_occupations := constraints.get("allowed_occupations")):
        if "所有人群" not in allowed_occupations and occupation not in allowed_occupations:
            return False
    if (forbidden_occupations := constraints.get("forbidden_occupations")) and occupation in forbidden_occupations:
        return False

    # Topic check
    if (allowed_topics := constraints.get("allowed_topics")):
        if "所有话题" not in allowed_topics and topic not in allowed_topics:
            return False
    if (forbidden_topics := constraints.get("forbidden_topics")) and topic in forbidden_topics:
        return False

    return True
# --- 结束复制的部分 ---

def calculate_all_valid_combinations(mode: str = 'k12'):
    """
    计算所有有效的画像组合数量。
    :param mode: 'k12' 或 'high'，决定使用哪套配置。
    """
    print(f"\n--- Calculating for [{mode.upper()}] mode ---")

    # 根据模式选择配置文件和常量
    if mode == 'high':
        core_drives_file = "high_core_drives.yaml"
        reaction_patterns_file = "high_reaction_patterns.yaml"
        topic_map = HIGH_TOPIC_SUBTOPIC_MAP
        occupations_list = HIGH_OCCUPATIONS_LIST
    elif mode == 'universal':
        core_drives_file = "universal_core_drives.yaml"
        reaction_patterns_file = "universal_reaction_patterns.yaml"
        topic_map = UNIVERSAL_TOPIC_SUBTOPIC_MAP
        occupations_list = UNIVERSAL_OCCUPATIONS_LIST
    else:  # 默认为 k12
        core_drives_file = "core_drives.yaml"
        reaction_patterns_file = "reaction_patterns.yaml"
        topic_map = TOPIC_SUBTOPIC_MAP
        occupations_list = OCCUPATIONS_LIST

    try:
        core_drives = load_yaml_file(Path(core_drives_file), ["name", "description"])
        reaction_patterns = load_yaml_file(Path(reaction_patterns_file), ["name", "description"])
    except Exception as e:
        print(f"Error loading YAML files for mode '{mode}': {e}")
        return 0

    valid_combination_count = 0
    
    # 构建所有主题 -> 子主题的扁平列表，用于迭代
    all_topic_subtopic_pairs = []
    for topic, subtopics_list in topic_map.items():
        for subtopic in subtopics_list:
            all_topic_subtopic_pairs.append({"topic": topic, "subtopic": subtopic})

    # 遍历所有可能的组合
    # product creates an iterator of Cartesian product of input iterables.
    # OCCUPATIONS_LIST
    # all_topic_subtopic_pairs (each item is a dict with "topic" and "subtopic")
    # core_drives
    # reaction_patterns
    
    print(f"Starting combination check...")
    print(f"Occupations: {len(occupations_list)}")
    print(f"Topic-Subtopic Pairs: {len(all_topic_subtopic_pairs)}")
    print(f"Core Drives: {len(core_drives)}")
    print(f"Reaction Patterns: {len(reaction_patterns)}")

    total_possible_unconstrained = len(occupations_list) * len(all_topic_subtopic_pairs) * len(core_drives) * len(reaction_patterns)
    print(f"Total possible unconstrained combinations (Occupation x SubTopic x Drive x Reaction): {total_possible_unconstrained}")

    processed_combinations = 0
    for occ in occupations_list:
        for ts_pair in all_topic_subtopic_pairs:
            topic = ts_pair["topic"]
            # subtopic = ts_pair["subtopic"] # subtopic is part of the unique combo, but not used in validation directly
            for drive in core_drives:
                for rp in reaction_patterns:
                    processed_combinations += 1
                    if processed_combinations % 10000 == 0:
                        print(f"Processed {processed_combinations}/{total_possible_unconstrained} potential combinations...")
                        
                    # 约束检查
                    # 1. 核心驱动对于 (职业, 主题) 是否有效
                    drive_valid = is_combination_valid(occ, topic, drive)
                    # 2. 反应模式对于 (职业, 主题) 是否有效
                    rp_valid = is_combination_valid(occ, topic, rp)

                    if drive_valid and rp_valid:
                        valid_combination_count += 1
    
    print(f"Finished processing. Total processed combinations: {processed_combinations}")
    return valid_combination_count

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate the total number of valid unique persona combinations based on archetype mode.")
    parser.add_argument(
        '--mode', 
        type=str, 
        default='universal', 
        choices=['k12', 'high', 'universal'], 
        help="The archetype mode to calculate for ('k12', 'high', or 'universal')."
    )
    args = parser.parse_args()

    print(f"Calculating the total number of valid unique persona combinations for mode: '{args.mode}'...")
    count = calculate_all_valid_combinations(mode=args.mode)
    print(f"\n理论上可生成的有效角色画像组合总数 ({args.mode.upper()} mode): {count}") 