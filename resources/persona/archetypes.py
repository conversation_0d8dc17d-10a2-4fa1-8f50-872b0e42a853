# archetypes_v3_simplified.py
# -*- coding: utf-8 -*-
"""
本脚本是角色画像生成器的第三版（精简版），专注于通过"策略性组合"方法，
自动化生成高度多样化、真实且具有内在逻辑一致性的K12心理咨询模拟用户角色卡（Personas）。

核心生成哲学：三维正交与策略增强
本版本采用"核心驱动 x 困扰情境 x 反应模式"三维正交组合的生成逻辑，以最大化多样性。
- 核心驱动 (Core Drive): 定义角色的"灵魂"，即其行为的内在动机。
- 困扰情境 (Situation): 定义角色面临的外部问题。
- 反应模式 (Reaction Pattern): 定义角色在压力下的外在行为表现。

同时，通过"策略性增强"配置，可以强制生成特定数量的稀有但关键的"边缘案例"，
以确保训练出的AI模型具有足够的鲁棒性来应对真实世界的复杂情况。

功能亮点:
1.  **模块化配置**: 所有生成维度（核心驱动、反应模式）均通过外部YAML文件配置，易于扩展和维护。
2.  **策略性组合生成**: 以组合爆炸（Combinatorial）为基础，极大化生成角色的多样性，并通过策略性配置（Strategic Configs）来确保关键案例的覆盖。
3.  **深度角色构建**: 指导LLM为每个角色创造独特的背景故事（解释核心驱动的来源），确保每个角色都有"历史"和"灵魂"。
4.  **强大的LLM兼容性与健壮性**: 支持OpenAI和Gemini，并包含连接性检查、并行处理和详细的错误处理。

如何使用:
1.  确保 `core_drives.yaml`, `reaction_patterns.yaml`, `system_prompt.txt` 文件与本脚本在同一目录下。
2.  在 `.env` 文件中配置您的LLM API密钥。
3.  在脚本末尾的 `if __name__ == "__main__":` 部分，配置 `TOTAL_PROFILES_TO_GENERATE` 和 `STRATEGIC_CONFIGS`。
4.  运行脚本: `python archetypes_v3_simplified.py`
5.  生成的角色卡将保存在 `generated_character_profiles_v3.json` 文件中。
"""

import json
import os
import yaml
import threading
import sys
import random
import uuid
import re  # 引入正则表达式库用于清洗
from pathlib import Path
from dotenv import load_dotenv
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from itertools import product
import traceback # Ensure traceback is imported

# --- LLM Utils Import ---
from tools.llm_utils import call_llm_api, check_llm_connectivity, LLM_PROVIDER
# ------------------------

load_dotenv()

# --- Archetype Mode ---
ARCHETYPE_MODE = os.getenv("ARCHETYPE_MODE", "universal") # k12, high, or universal

# --- 预设的理想字段 ---
DESIRED_FIELDS = [
    "Gender", "Age", "Occupation", "Topic", "Subtopic", "Situation", "Personality",
    "Event Time", "Event Location", "Event Participants", "Event Description",
    "Emotional Experience Words", "Coped Strategies and Effects", "Goals and Expectations",
    "persona_id", "Core Drive", "Reaction Pattern"
]

# --- Big Five Personality Model ---
BIG_FIVE_PERSONALITY_TRAITS = [
    ("Openness", "Closedness"),
    ("Conscientiousness", "Unconscientiousness"),
    ("Extraversion", "Intraversion"),
    ("Agreeableness", "Antagonism"),
    ("Neuroticism", "Emotional Stability")
]
# -----------------------------

# --- 全局配置与锁 ---
MAX_WORKERS = int(os.getenv("MAX_WORKERS", 256))
shared_list_lock = threading.Lock()
# --------------------

# --- 资产加载 ---
# K12
TOPIC_SUBTOPIC_MAP = {
    "学业与成长": ["学业压力", "考试焦虑", "成绩困扰", "学习方法", "学习动力", "厌学情绪", "注意力不集中", "作业问题", "升学规划", "时间管理"],
    "人际与社交": ["同学关系", "交友困惑", "校园霸凌", "被孤立", "沟通技巧", "师生关系", "网络交友", "人际边界", "信任问题", "社交恐惧"],
    "家庭与亲子": ["亲子沟通", "家庭压力", "父母期望", "家庭矛盾", "家庭暴力", "缺乏理解", "隐私空间"],
    "情绪与内在状态": ["创伤事件复述与应激", "抑郁低落与绝望", "焦虑烦躁与失控", "孤独感与无价值感", "自伤与自杀念头", "冲动与愤怒管理", "网络/手机成瘾", "躯体化症状"],
    "自我认知与价值观": ["自信心与自我价值", "外貌焦虑", "性格困扰", "理想与现实冲突", "个人成长哲学", "生命意义与道德思辨", "青春期萌动"],
    "互动模式与对话探索": ["AI身份试探", "测试系统边界", "幽默/游戏式互动", "哲学式辩论", "碎片化/回避式表达", "寻求角色扮演"],
    "家长视角与求助": ["孩子社交问题", "孩子情绪行为异常", "孩子学业求助", "亲子沟通方法", "如何引导与帮助孩子", "担忧孩子网络使用"]
}
OCCUPATIONS_LIST = ["小学生", "初中生", "高中生", "老师", "家长"]
# 高校
HIGH_TOPIC_SUBTOPIC_MAP = {
    "学业与成长": ["学业压力", "考试焦虑", "成绩困扰", "学习方法", "学习习惯", "厌学情绪", "升学规划", "作业问题", "时间管理"],
    "人际与社交": ["同学关系", "交友困惑", "校园霸凌", "人际边界", "师生关系", "网络交友"],
    "恋爱与情感": ["择偶标准", "亲密关系", "恋爱困扰", "分手失恋", "情感表达", "恋爱观念", "暗恋/单恋"],
    "职业与规划": ["就业焦虑", "职业发展", "实习困扰", "职场人际", "工作压力", "职业规划"],
    "家庭与亲子": ["亲子沟通", "家庭压力", "家庭矛盾", "家庭暴力"],
    "情绪与内在状态": ["行为习惯", "焦虑烦躁与失控", "抑郁低落与绝望", "孤独感与无价值感", "自伤与自杀念头", "冲动与愤怒管理", "网络/手机成瘾", "创伤事件复述与应激"],
    "自我认知与价值观": ["自信心与自我价值", "外貌焦虑", "性格困扰", "生命意义与道德思辨"]
}

HIGH_OCCUPATIONS_LIST = ["小学生", "初中生", "高中生", "大学生", "老师", "家长", "职场人士"]

# Universal
UNIVERSAL_TOPIC_SUBTOPIC_MAP = {
    "个人成长": [
        "自我探索与认知", "自信心提升", "个人价值感", "原生家庭影响", 
        "情绪管理", "压力应对", "时间管理", "习惯养成", "性格完善", "人生意义探索"
    ],
    "情绪困扰": [
        "抑郁情绪", "焦虑情绪", "恐惧情绪", "强迫现象", "双相情感障碍", 
        "疑病现象", "进食障碍", "愤怒与冲动管理", "孤独感", "成瘾行为"
    ],
    "人际关系": [
        "社交恐惧", "沟通技巧", "人际边界设定", "依赖共生", "分离个体化", 
        "讨好型人格", "家庭关系", "职场关系", "师生关系", "网络人际"
    ],
    "婚恋情感": [
        "脱单与吸引力", "情感挽回", "失恋修复", "婚姻经营", "亲密关系提升", 
        "出轨创伤", "分手与离婚抉择", "情感虐待识别", "性与亲密"
    ],
    "亲子教育": [
        "亲子沟通", "儿童青少年心理", "学业压力与厌学", "行为问题矫正", 
        "青春期议题", "家庭教育方式", "隔代教育", "网络沉迷引导"
    ],
    "职场发展": [
        "职业规划与选择", "职场减压", "人际沟通与办公室政治", "晋升瓶颈", 
        "职业倦怠", "工作生活平衡", "求职与面试", "能力提升"
    ],
    "心理创伤": [
        "童年创伤", "校园霸凌", "灾难事件应激", "情感虐待", 
        "丧失与哀悼", "意外事故后遗症", "家庭暴力"
    ],
    "性心理": [
        "性心理认知", "性少数群体(LGBTQ+)", "性功能障碍", "性癖好与特殊偏好", 
        "性压抑与羞耻感", "性与关系"
    ],
    "个人财务": [
        "财务压力", "理财规划", "消费习惯与观念", "债务问题"
    ],
    "互动与探索": [
        "AI身份试探", "测试系统边界", "角色扮演", "哲学思辨", "寻求具体信息"
    ]
}

UNIVERSAL_OCCUPATIONS_LIST = [
    # 学生阶段
    "小学生",
    "初中生",
    "高中生",
    "大学生",
    "研究生",

    # 职场角色
    "职场人士", # 通用
    "职场新人",
    "中层管理者",
    "高层管理者",
    "创业者",
    "自由职业者",
    "技术人员",
    "蓝领工人",
    "艺术家",

    # 教育与医护
    "老师",
    "医护人员",
    "心理咨询师",
    "社会工作者",

    # 家庭角色
    "家长", # 通用
    "全职父母",
    "需要照顾长辈的成年子女",

    # 其他社会身份
    "退休人员",
    "失业/待业者",
    "长期病患",
    "经历重大丧失者"
]


ALLOWED_EMOTIONS = ["恐惧", "焦虑", "迷茫", "无助", "逃避", "心虚", "压抑", "羞愧", "烦躁", "内疚", "麻木", "沮丧"]

def load_yaml_file(file_path: Path, required_keys: list):
    if not file_path.exists():
        raise FileNotFoundError(f"Required YAML file not found: {file_path}")
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        if not isinstance(data, list):
            raise ValueError(f"Invalid format in {file_path}. Expected a list of dictionaries.")
        for i, item in enumerate(data):
            if not all(key in item for key in required_keys):
                raise ValueError(f"Item at index {i} in '{file_path}' is missing one of required keys: {required_keys}.")
            item["constraints"] = item.get("constraints", {})
        print(f"Successfully loaded {len(data)} items from '{file_path}'.")
        return data
    except (yaml.YAMLError, ValueError) as e:
        print(f"Error processing YAML file '{file_path}': {e}")
        raise

def load_text_file(file_path: Path):
    if not file_path.exists():
        raise FileNotFoundError(f"Required text file not found: {file_path}")
    with open(file_path, 'r', encoding='utf-8') as f:
        return f.read()

try:
    if ARCHETYPE_MODE == "k12":
        core_drives_file = "core_drives.yaml"
        reaction_patterns_file = "reaction_patterns.yaml"
        system_prompt_file = "system_prompt.txt"
    elif ARCHETYPE_MODE == "high":
        core_drives_file = "high_core_drives.yaml"
        reaction_patterns_file = "high_reaction_patterns.yaml"
        system_prompt_file = "high_system_prompt.txt"
    elif ARCHETYPE_MODE == "universal":
        core_drives_file = "universal_core_drives.yaml"
        reaction_patterns_file = "universal_reaction_patterns.yaml"
        system_prompt_file = "universal_system_prompt.txt"
    else:
        print(f"Invalid ARCHETYPE_MODE: {ARCHETYPE_MODE}. Please use 'k12', 'high', or 'universal'.")
        sys.exit(1)

    CORE_DRIVES = load_yaml_file(Path(core_drives_file), ["name", "description"])
    REACTION_PATTERNS = load_yaml_file(Path(reaction_patterns_file), ["name", "description"])
    SYSTEM_PROMPT_TEMPLATE = load_text_file(Path(system_prompt_file))

except (FileNotFoundError, ValueError) as e:
    print(f"Fatal error loading configuration files: {e}. Exiting.")
    sys.exit(1)

# --- Mode-specific data selection ---
if ARCHETYPE_MODE == "k12":
    CURRENT_OCCUPATIONS = OCCUPATIONS_LIST
    CURRENT_TOPIC_MAP = TOPIC_SUBTOPIC_MAP
elif ARCHETYPE_MODE == "high":
    CURRENT_OCCUPATIONS = HIGH_OCCUPATIONS_LIST
    CURRENT_TOPIC_MAP = HIGH_TOPIC_SUBTOPIC_MAP
elif ARCHETYPE_MODE == "universal":
    CURRENT_OCCUPATIONS = UNIVERSAL_OCCUPATIONS_LIST
    CURRENT_TOPIC_MAP = UNIVERSAL_TOPIC_SUBTOPIC_MAP
else:
    # This path should ideally not be reached due to the check during file loading
    print(f"FATAL: Unknown ARCHETYPE_MODE '{ARCHETYPE_MODE}'. Please use 'k12', 'high', or 'universal'.")
    sys.exit(1)


# --- 约束校验函数 ---
def is_combination_valid(occupation: str, topic: str, config_item: dict) -> bool:
    """通用约束检查函数，增加了对'所有话题'和'所有人群'通配符的支持。"""
    constraints = config_item.get("constraints", {})
    if not isinstance(constraints, dict): return True  # No constraints means valid

    # Occupation check
    if (allowed_occupations := constraints.get("allowed_occupations")):
        if "所有人群" not in allowed_occupations and occupation not in allowed_occupations:
            return False
    if (forbidden_occupations := constraints.get("forbidden_occupations")) and occupation in forbidden_occupations:
        return False

    # Topic check
    if (allowed_topics := constraints.get("allowed_topics")):
        if "所有话题" not in allowed_topics and topic not in allowed_topics:
            return False
    if (forbidden_topics := constraints.get("forbidden_topics")) and topic in forbidden_topics:
        return False
        
    return True

# --- 核心生成逻辑 ---
def process_generation_task(task):
    """处理单个生成任务，调用LLM并返回结果。"""
    try:
        # DEBUG: Print task details
        # print(f"DEBUG: Starting process_generation_task with task: {task}")
        
        # --- 使用用户定义的全部情绪并格式化为字符串 ---
        allowed_emotions_str = ", ".join(ALLOWED_EMOTIONS) # 使用全局定义的 ALLOWED_EMOTIONS
        # --- 结束 ---

        # --- 新增：构建人格特质说明 ---
        personality_dimensions_info = []
        # 为了让LLM更好地理解，这里用了中文标签
        for dimension, (trait1, trait2) in zip(
            ["开放性 (Openness)", "责任心 (Conscientiousness)", "外向性 (Extraversion)", "宜人性 (Agreeableness)", "神经质 (Neuroticism)"],
            BIG_FIVE_PERSONALITY_TRAITS
        ):
            personality_dimensions_info.append(f"- {dimension}: 从中选择一个: ['{trait1}', '{trait2}']")

        personality_instructions = (
            "基于角色的核心驱动、情境和反应模式，请使用五大人格模型来判断其性格。"
            "在返回的'Personality'字段中，请提供一个包含5个字符串的JSON数组。"
            "对于以下五个维度，你必须为每个维度精确选择一个最符合该角色的特质:\n"
            + "\n".join(personality_dimensions_info)
        )
        # --- 结束 ---

        system_prompt = SYSTEM_PROMPT_TEMPLATE.format(
            target_core_drive_name=task["core_drive"]["name"],
            target_core_drive_description=task["core_drive"]["description"],
            target_topic=task["topic"],
            target_subtopic=task["subtopic"],
            target_reaction_pattern_name=task["reaction_pattern"]["name"],
            target_reaction_pattern_description=task["reaction_pattern"]["description"],
            target_occupation=task["occupation"],
            allowed_emotions=allowed_emotions_str, # 传递格式化后的字符串
            personality_instructions=personality_instructions # 传递人格指令
        )
        
        # DEBUG: Print formatted prompt
        # print(f"DEBUG: System prompt formatted: {system_prompt[:200]}...") # Print first 200 chars
        
        profile_json = call_llm_api(system_prompt)
        
        # DEBUG: Print LLM response
        # if profile_json is not None:
        #     print(f"DEBUG: LLM call returned type: {type(profile_json)}, content: {str(profile_json)[:200]}...")
        # else:
        #     print("DEBUG: LLM call returned None")

        if profile_json and isinstance(profile_json, dict):
            profile_json['persona_id'] = str(uuid.uuid4())
            
            # 从任务定义中注入核心驱动和反应模式，以便在最终输出中追踪它们
            profile_json['Core Drive'] = task['core_drive']['name']
            profile_json['Reaction Pattern'] = task['reaction_pattern']['name']
            
            # --- START OF RE-ADDED AGE PROCESSING CODE ---
            if 'Age' in profile_json:
                age_value = profile_json['Age']
                if isinstance(age_value, str):
                    try:
                        profile_json['Age'] = int(age_value)
                    except ValueError:
                        print(f"  Warning: Could not convert Age string '{age_value}' to int for persona_id {profile_json.get('persona_id', 'N/A')}. Setting Age to None.")
                        profile_json['Age'] = None
                elif not isinstance(age_value, int) and age_value is not None: # Check if not int AND not None
                    original_age_type = type(age_value).__name__
                    print(f"  Warning: Age field for persona_id {profile_json.get('persona_id', 'N/A')} was of unexpected type ({original_age_type}) with value '{age_value}'. Setting Age to None.")
                    profile_json['Age'] = None
                # If Age is already an int or None, do nothing.
            # else:
                # Optional: Log if 'Age' field is entirely missing from LLM output if it's expected.
                # print(f"  Info: 'Age' field missing in raw LLM output for persona_id {profile_json.get('persona_id', 'N/A')}.")
            # --- END OF RE-ADDED AGE PROCESSING CODE ---
            
            # --- START OF RE-ADDED UNEXPECTED FIELDS IDENTIFICATION ---
            # Identify unexpected fields *after* Age processing but *before* DESIRED_FIELDS filtering
            all_keys_after_age_processing = set(profile_json.keys())
            desired_keys_set = set(DESIRED_FIELDS)
            unexpected_keys_generated_by_llm = list(all_keys_after_age_processing - desired_keys_set)
            # --- END OF RE-ADDED UNEXPECTED FIELDS IDENTIFICATION ---

            # --- 新增字段过滤功能 ---
            filtered_profile = {key: profile_json[key] for key in DESIRED_FIELDS if key in profile_json}
            # 检查是否有未包含在 DESIRED_FIELDS 中的原始 profile_json 字段 (可选的警告)
            
            # --- START OF RE-ADDED UNEXPECTED FIELDS WARNING (adjusted placement) ---
            # Warn about unexpected fields that were removed by the DESIRED_FIELDS filter
            if unexpected_keys_generated_by_llm:
                 task_identifier_for_log = f"persona_id {profile_json.get('persona_id', 'N/A')}" # Use the already generated ID
                 print(f"  Info: For {task_identifier_for_log}, the LLM generated unexpected fields which were subsequently removed by the DESIRED_FIELDS filter: {unexpected_keys_generated_by_llm}")
            # --- END OF RE-ADDED UNEXPECTED FIELDS WARNING ---
            
            return filtered_profile
            # --- 结束新增 ---
        else:
            print(f"  Warning: LLM call failed or returned invalid/non-dict data for a task (type: {type(profile_json)}). Skipping.")
            return None
    except Exception as e:
        print(f"  Error processing task (Exception Type: {type(e)}): {e}. Skipping.")
        print("  Traceback:")
        traceback.print_exc() # This will print the full stack trace
        return None

def generate_profiles(num_profiles: int, strategic_configs: list = []):
    """主生成函数，采用策略性组合生成模式。"""
    generation_plan = []
    remaining_profiles = num_profiles

    # 1. 应用策略性增强配置
    print("\n--- Applying Strategic Configurations ---")
    if strategic_configs:
        for config in strategic_configs:
            count = config.get("count", 0)
            if count <= 0: continue
            
            possible_drives = [cd for cd in CORE_DRIVES if not config.get("core_drive") or cd['name'] == config.get("core_drive")]
            possible_occupations = [occ for occ in CURRENT_OCCUPATIONS if not config.get("occupation") or occ == config.get("occupation")]
            possible_topics = [t for t in CURRENT_TOPIC_MAP.keys() if not config.get("topic") or t == config.get("topic")]
            possible_rps = [rp for rp in REACTION_PATTERNS if not config.get("reaction_pattern") or rp['name'] == config.get("reaction_pattern")]
            
            if not all([possible_drives, possible_occupations, possible_topics, possible_rps]):
                print(f"Warning: A strategic config could not find matching items. Config: {config}")
                continue

            for _ in range(count):
                if remaining_profiles <= 0: break
                
                drive = random.choice(possible_drives)
                occupation = random.choice(possible_occupations)
                topic = random.choice(possible_topics)
                subtopic = random.choice(CURRENT_TOPIC_MAP[topic])
                rp = random.choice(possible_rps)

                if is_combination_valid(occupation, topic, drive) and is_combination_valid(occupation, topic, rp):
                    generation_plan.append({
                        "occupation": occupation, "topic": topic, "subtopic": subtopic,
                        "core_drive": drive, "reaction_pattern": rp
                    })
                    remaining_profiles -= 1
    
    print(f"Strategically planned {num_profiles - remaining_profiles} profiles. {remaining_profiles} remaining for combinatorial generation.")

    # 2. 构建组合模式的生成计划 (为剩余数量)
    if remaining_profiles > 0:
        print(f"\n--- Building Plan for remaining {remaining_profiles} Profiles (Combinatorial Mode) ---")
        all_possible_combos = list(product(CURRENT_OCCUPATIONS, CURRENT_TOPIC_MAP.items(), CORE_DRIVES, REACTION_PATTERNS))
        valid_combos = []
        for occ, (top, subs), drive, rp in all_possible_combos:
            if is_combination_valid(occ, top, drive) and is_combination_valid(occ, top, rp):
                for sub in subs:
                    valid_combos.append({
                        "occupation": occ, "topic": top, "subtopic": sub,
                        "core_drive": drive, "reaction_pattern": rp
                    })
        
        if not valid_combos:
            print("Warning: No valid combinations found in combinatorial mode. Cannot generate remaining profiles.")
        else:
            num_to_sample = min(remaining_profiles, len(valid_combos))
            generation_plan.extend(random.sample(valid_combos, num_to_sample))
            if num_to_sample < remaining_profiles:
                print(f"Warning: Only found {num_to_sample} unique valid combinations. Total generation will be less than requested.")

    if not generation_plan:
        print("Generation plan is empty. No profiles will be generated.")
        return []

    # 3. 执行生成
    print(f"\n--- Executing Generation Plan: {len(generation_plan)} profiles to create ---")
    all_profiles = []
    with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = {executor.submit(process_generation_task, task): task for task in generation_plan}
        with tqdm(total=len(futures), desc="Generating Profiles") as pbar:
            for future in as_completed(futures):
                result = future.result()
                if result:
                    all_profiles.append(result)
                pbar.update(1)

    print(f"\n--- Generation Complete: Successfully generated {len(all_profiles)} profiles. ---")
    return all_profiles

# --- 主执行部分 ---
if __name__ == "__main__":
    if not check_llm_connectivity():
        sys.exit(1)
        
    # --- 配置 ---
    # 要生成的总画像数
    # TOTAL_PROFILES_TO_GENERATE = 2520 # k12
    # TOTAL_PROFILES_TO_GENERATE = 3394 # high
    TOTAL_PROFILES_TO_GENERATE = 5000 # universal
    
    # 策略性增强配置 (可选，可为空列表 [])
    STRATEGIC_CONFIGS = []
    """
    STRATEGIC_CONFIGS = [
        {
            "reaction_pattern": "绝望求死与危机边缘型",
            "count": 1 # 强制生成1个危机边缘型画像
        },
        {
            "reaction_pattern": "家长视角与求助",
            "count": 2 # 强制生成2个家长视角与求助画像
        }
        # 您可以在此处添加更多策略性配置
    ]
    """
    # --- 结束配置 ---

    print(f"--- Running Archetype Generator ---")
    print(f"Mode: Strategic Combinatorial, Target Profiles: {TOTAL_PROFILES_TO_GENERATE}")

    generated_profiles = generate_profiles(
        num_profiles=TOTAL_PROFILES_TO_GENERATE,
        strategic_configs=STRATEGIC_CONFIGS
    )

    # --- 保存结果 ---
    if generated_profiles:
        output_filename = f"generated_character_profiles_{ARCHETYPE_MODE}_{TOTAL_PROFILES_TO_GENERATE}.json"
        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(generated_profiles, f, ensure_ascii=False, indent=2)
            print(f"\nAll {len(generated_profiles)} profiles saved to: {output_filename}")
        except IOError as e:
            print(f"\nError saving file '{output_filename}': {e}.")
    else:
        print("\nNo character profiles were generated in this run.")

    print(f"\nLLM Provider used was: {LLM_PROVIDER}")

