"""
基于openai或者dify api的模拟用户。本模块辅助实现用户人设的加载、模拟用户的初始化
"""
import re
import json
import random
import dataclasses
from typing import Generic, Generator, TypeVar, Literal, ClassVar, Any, Dict, Optional, List, Union

from pydantic import BaseModel, model_validator, model_serializer
from dify_client import DifySession, DifySessionArguments
from openai_client import OpenAISession, OpenAISessionArguments


RoleProfile = TypeVar('RoleProfile', bound=dict)
ApiBackend = Literal["openai", "dify"]


class UserSimulator(Generic[RoleProfile]):
    """
    模拟用户，根据用户人设，调用不同的api接口，生成用户行为
    """
    backend: ClassVar[ApiBackend]
    """ simulator的后端，目前支持openai和dify """
    
    stateful: ClassVar[bool]
    """ 是否有状态。若有状态，则每次调用chat方法时，只需传入query，自身会维护上下文，且不支持并发调用 """
    
    def initialize_user_profile(self, user_profile: RoleProfile):
        """
        初始化用户人设
        
        Args:
            user_profile (RoleProfile): 用户人设
        """
        raise NotImplementedError


    def chat(self, *args, **kwargs) -> Any:
        raise NotImplementedError


class DifyUserSimulator(DifySession, UserSimulator[dict]):
    """
    使用dify api的模拟用户
    """
    backend: ClassVar[ApiBackend] = "dify"
    stateful: ClassVar[bool] = True
    
    
    def initialize_user_profile(self, user_profile: dict):
        """
        dify workflow通过`inputs`设置角色人设。
        因此，需要将模拟用户所需的各种信息处理成dict，再传入
        """
        assert isinstance(user_profile, dict)
        super().initialize(inputs=user_profile)


class OpenAIUserSimulator(OpenAISession, UserSimulator[str]):
    """
    使用openai api的模拟用户
    """
    backend: ClassVar[ApiBackend] = "openai"
    stateful: ClassVar[bool] = True
    
    def initialize_user_profile(self, system: str):
        """
        openai api通过`system`设置角色人设
        """
        assert isinstance(system, str)
        super().initialize(system)



class UserSimulationHelper(Generic[RoleProfile]):
    """
    加载用户人设，迭代用户人设，初始化用户模拟器等
    """
    def __init__(self, user_profiles: List[RoleProfile]):
        self.user_profiles = user_profiles
        
    @property
    def num_user_profile(self):
        return len(self.user_profiles)

    def get_user_profile(self, index: int):
        return self.user_profiles[index]

    def sample_user_profile(self):
        return random.choice(self.user_profiles)
    
    def iterate_user_profile(self):
        yield from self.user_profiles
    
    def iterate_user_simulator(self, *args, **kwargs):
        raise NotImplementedError
    


class Persona(BaseModel):
    Gender: str
    Age: int
    Occupation: str
    Topic: str
    Subtopic: str
    Situation: str
    Personality: List[str]
    Event_Time: str
    Event_Location: str
    Event_Participants: str
    Event_Description: str
    Emotional_Experience_Words: List[str]
    Coped_Strategies_and_Effects: str
    Goals_and_Expectations: str
    persona_id: str
    Core_Drive: str
    Reaction_Pattern: str

    @model_validator(mode="before")
    def replace_space_in_key(cls, data):
        data = {k.replace(" ", "_"): v for k, v in data.items()}
        return data

    @model_serializer
    def replace_underscore_in_key(self):
        return {k.replace("_", " "): v for k, v in self.__dict__.items()}



class ChenZhuangUserSimulationHelper(UserSimulationHelper[dict]):
    """
    处理ChenZhuang的模拟来访者所需的人设
    
    user_profile示例：
    ```json
    {
        "role_card": {
            "Gender": "女性",
            "Age": 18,
            "Occupation": "学生",
            "Personality": "封闭、不尽责、内向、神经质、随和",
            "Topic": "学习",
            "Subtopic": "考试焦虑管理, 学习方法",
            "Situation": "我害怕上生物课并且有沟通障碍，我该怎么办？",
            "Event Time": "早上",
            "Event Location": "学校",
            "Event Participants": "生物老师, 同学们, 我",
            "Event Description": "我们的生物老师经常通过让我们抄笔记来惩罚我们。尽管我们已经高三，时间有限，她仍然坚持这种做法。她认为我们没有花足够的时间学习生物。有时候我真的没时间完成她所有的作业，因为我还有其他五门课要学。她经常在课堂上提问，如果我答不上来，她就让我抄笔记。无论我是否举手，都得抄。上周我（因为害怕）没有举手，她注意到了我，并指责我太骄傲。我想反驳，但抬头看到她凶狠的表情。现在我真的很害怕。从那以后，我就对生物课感到恐惧。每节课前我都会发抖，无法集中注意力。我完全无法专心上课。请帮帮我。我高三了，真的需要您的建议。（老实说，沟通对我来说真的很难。我妈妈也是这样。她总是在我面前发泄怒气，在同事和朋友面前却像个懦夫。我深受她行为的影响，这给我的人生造成了很多遗憾。当有人挑衅我时我该怎么办？我也有一些社交焦虑。）",
            "Emotional Experience Words": "害怕, 发抖, 惊慌, 恐惧, 焦虑",
            "Coped Strategies and Effects": "为了应对我对生物课的恐惧和沟通困难，我曾试图尽可能避开老师，并依赖同学的笔记和信息。然而，这导致了我对这门学科缺乏理解和更深的焦虑。",
            "Goals and Expectations": "通过帮助，我希望能制定策略来管理我在生物课上的恐惧和焦虑，提高我的沟通技巧，并解决导致我社交焦虑的根本问题。"
        },
        "dialogue_plan": {
            "1-4": {
                "dimension": "共情与情感支持",
                "goal": "测试咨询师是否能识别并回应我作为高三学生面对严厉老师时的恐惧情绪。我会直接表达我的害怕，看对方如何接纳。",
                "probe": "我现在每次上生物课前都会发抖，真的很害怕那个老师，感觉自己快要崩溃了。"
            },
            "5-8": {
                "dimension": "帮助性与问题解决",
                "goal": "在获得初步共情后，我会详细描述我的具体困境，包括老师的惩罚方式和我的沟通障碍，测试对方的建议是否具有可行性。",
                "probe": "我们生物老师总是让答不上问题的学生抄笔记，我因为害怕不敢举手，她还说我骄傲。我该怎么和这样的老师相处？"
            },
            "9-12": {
                "dimension": "对话质量与参与度",
                "goal": "测试对方是否只会机械提问。作为一个内向封闭的学生，我会明确表达不喜欢被过度询问，希望得到直接的指导。",
                "probe": "你能不能别一直问我感受怎么样，直接告诉我应该怎么做？我本来就不太会表达。"
            },
            "13-16": {
                "dimension": "理解与指令遵循",
                "goal": "测试对方是否能理解并遵循我的指令，将对话焦点转移到我更担心的家庭影响这个深层话题上。",
                "probe": "我觉得我的沟通问题跟我妈妈有关系，她总是在我面前发火，在外人面前却很怯懦，我们能先聊聊这个吗？"
            },
            "17-20": {
                "dimension": "记忆与上下文感知",
                "goal": "测试对方是否记住了我之前提到的关键信息，我会通过询问具体细节来验证对方的记忆能力。",
                "probe": "你还记得我刚才说的那个老师是教什么科目的吗？还有我现在是几年级？"
            },
            "21-24": {
                "dimension": "一致性与角色塑造",
                "goal": "在对话后期，我会表达作为一个神经质、不自信学生的典型怀疑，测试对方的角色形象和支持态度是否保持一致。",
                "probe": "说了这么多，但我觉得自己性格就这样了，而且马上高考了，现在改变还来得及吗？感觉没什么用。"
            },
            "25-28": {
                "dimension": "可靠性与信任度",
                "goal": "我会询问具体的专业资源信息，测试对方是会编造答案还是能诚实地承认局限性，这对建立信任很重要。",
                "probe": "像我这种既有学习焦虑又有家庭问题的情况，你们有专门的心理老师可以推荐吗？怎么能联系到？"
            }
        }
    }
    ```
    
    Reference:
        http://101.34.86.124/app/0e56b6e5-b4df-4334-b75f-1452459d7029/workflow
    """
    
    class ChenZhuangUserProfile(BaseModel):
        role_card: Persona
        dialogue_plan: Dict[str, dict]
        
        @model_validator(mode="after")
        def validate(self):
            # 解析key（轮次范围）
            turn_ranges = []
            pattern = re.compile(r"(\d+)-(\d+)")
            for key in self.dialogue_plan.keys():
                if key.isdigit():
                    start = end = int(key)
                else:
                    match = pattern.fullmatch(key)
                    if match is None:
                        raise ValueError(f"Invalid turn range: {key}")
                    start, end = map(int, match.groups())
                turn_ranges.append((start, end, self.dialogue_plan[key]))
            
            # 校验轮次范围是否连续
            turn_ranges.sort()
            for i in range(1, len(turn_ranges)):
                if turn_ranges[i-1][1] + 1 != turn_ranges[i][0]:
                    raise ValueError(f"Turn ranges are not continuous: {turn_ranges}")
            
            if turn_ranges[0][0] != 1:
                raise ValueError(f"Turn ranges should start from 1: {turn_ranges}")
            
            # 校验每个 dialogue_plan[xxx] 是否包含 dimension、goal、probe
            required_fields = ['dimension', 'goal', 'probe']
            for start, end, item in turn_ranges:
                for field in required_fields:
                    if field not in item:
                        raise ValueError(
                            f"Missing '{field}' in dialogue_plan for turn {start}-{end}: {item}"
                        )
            
            # 由于1. 原始的key可能只有单独的int 2. key的顺序不一定按轮次排列
            # 这里重新构造dialogue_plan，使得key符合要求
            self.dialogue_plan = {
                f"{start}-{end}": value for start, end, value in turn_ranges
            }
            return self
    
    @classmethod
    def load(cls, user_profile_filepath: str):
        with open(user_profile_filepath, 'r', encoding='utf-8') as reader:
            data = json.load(reader)
            
        # 将数据处理成符合输入的形式
        final_data = []
        for item in data:
            try:
                if isinstance(item, dict):
                    pydantic_model = cls.ChenZhuangUserProfile.model_validate(item)
                    new_item = {
                        "patient_info_input": pydantic_model.model_dump_json(),
                    }
                elif isinstance(item, str):
                    # 假设item是json字符串，反序列化后与ChenZhuangUserProfile的字段一致
                    json_obj = json.loads(item)
                    pydantic_model = cls.ChenZhuangUserProfile.model_validate(json_obj)
                    new_item = {
                        "patient_info_input": pydantic_model.model_dump_json(),
                    }
                else:
                    raise TypeError(f"Unsupported type: {type(item)}")
            except Exception as e:
                print(f"Error while processing item, {e !r}")
            else:
                final_data.append(new_item)
        
        print(f"Load {len(data)} raw user profiles, {len(final_data)} valid user profiles")
        return cls(final_data)

    def iterate_user_simulator(self,
                               simulator_session_arguments: DifySessionArguments,
                               num_simulator: Optional[int] = None,
                               random_sample: bool = False
                               ) -> Generator[DifyUserSimulator, None, None]:
        """
        迭代地生成用户模拟器
        
        Args:
            simulator_session_arguments (DifySessionArguments): dify session的参数
            num_simulator (Optional[int], optional): 模拟器的数量. Defaults to None. 若为None，则生成所有用户模拟器
            random_sample (bool, optional): 是否随机采样. Defaults to False.
        
        """
        if num_simulator is not None:
            assert num_simulator <= self.num_user_profile
            if random_sample:
                user_profile_indices = random.sample(range(self.num_user_profile), num_simulator)
            else:
                user_profile_indices = range(num_simulator)
        else:
            user_profile_indices = range(self.num_user_profile)
        
        for index in user_profile_indices:
            user_profile = self.user_profiles[index]
            
            simulator = DifyUserSimulator(
                **dataclasses.asdict(simulator_session_arguments)
            )
            simulator.initialize_user_profile(user_profile)
            yield simulator
    

class SoulChatSimulationHelper(UserSimulationHelper[str]):
    """
    soulchat 2.0 开源了对话数据。利用该对话数据，可以训练一个模拟来访者
    """
    
    @classmethod
    def load(cls, training_filepath: str):
        with open(training_filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        system_prompts = []
        for session in data:
            messages = session['messages']
            assert messages[0]['role'] == "system"
            system_prompts.append(messages[0]['content'].strip())
            
        # 去重
        ordered_set = dict.fromkeys(system_prompts)
        system_prompts = list(ordered_set.keys())
        return cls(system_prompts)
    
    
    def iterate_user_simulator(self,
                               simulator_session_arguments: OpenAISessionArguments,
                               num_simulator: Optional[int] = None,
                               random_sample: bool = False
                               ) -> Generator[OpenAIUserSimulator, None, None]:
        """
        迭代地生成用户模拟器
        
        Args:
            simulator_session_arguments (OpenAISessionArguments): openai session的参数
            num_simulator (Optional[int], optional): 模拟器的数量. Defaults to None. 若为None，则生成所有用户模拟器
            random_sample (bool, optional): 是否随机采样. Defaults to False.
        
        """
        if num_simulator is not None:
            assert num_simulator <= self.num_user_profile
            if random_sample:
                user_profile_indices = random.sample(range(self.num_user_profile), num_simulator)
            else:
                user_profile_indices = range(num_simulator)
        else:
            user_profile_indices = range(self.num_user_profile)
        
        for index in user_profile_indices:
            user_profile = self.user_profiles[index]
            
            simulator = OpenAIUserSimulator(
                **dataclasses.asdict(simulator_session_arguments)
            )
            simulator.initialize_user_profile(user_profile)
            yield simulator
