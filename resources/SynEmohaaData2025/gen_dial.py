"""
调用模拟用户和咨询师模型，完成交互对话
"""
import json
import os
import concurrent.futures
import dataclasses
from typing import Literal, Optional, Literal, Union
from pathlib import Path

from dotenv import load_dotenv
from tqdm import tqdm, trange
from dify_client import DifySession, DifySessionArguments
from openai_client import OpenAISession, OpenAISessionArguments
from user_simulator import ChenZhuangUserSimulationHelper, DifyUserSimulator, SoulChatSimulationHelper, OpenAIUserSimulator


load_dotenv()


def parse_assistant_content(response: str):
    """ response中可能包含思考过程，此函数从response中解析回复 """
    if '</think>' in response:
        response = response.split('</think>')[-1].strip()
    
    if "</details>" in response:
        content = response.split("</details>")[-1]
    else:
        content = response
    return content.strip()


greetings = [
  "您好，最近感觉有点困扰，想和您聊聊。",
  "老师好，最近心里有些压力，想寻求一些帮助。",
  "您好，第一次来做咨询，有点紧张，但希望能放松下来。",
  "最近总感觉不太对劲，想和专业人士谈谈。",
  "您好，之前预约了咨询，今天想和您沟通一些想法。",
  "最近情绪有些波动，想听听您的建议。",
  "老师好，虽然不太确定从何说起，但觉得需要和您交流。",
  "您好，生活中有些问题一直放不下，想试着梳理一下。",
  "最近有些困惑，今天想试着说出来。",
  "你好啊",
  "虽然不确定从何开始，但觉得需要和您聊聊。"
]

# TODO: num_turns应该根据 plan中的规划解析
def gen_dialogue(
    simulator_session: Union[DifyUserSimulator, OpenAIUserSimulator],
    assistant_session: Union[DifySession, OpenAISession],
    num_turns: int = 20,
    return_internal_assistant_data: bool = False,
    return_internal_user_data: bool = False,
    dialogue_index: int = 0
):
    """
    令simulator和assistant进行对话，生成对话数据
    """
    # 初始化用户和助手的上一轮对话内容
    prev_user_query = greetings[dialogue_index % len(greetings)]
    prev_assistant_query = ''
    # 初始化对话历史
    history = []
    # 将用户的第一轮对话内容添加到历史中
    history.append({"role": "user", "content": prev_user_query})
    
    # 循环num_turns次，进行对话
    for _ in trange(num_turns):
        try:
            # 首先调用咨询师
            if isinstance(assistant_session, DifySession):
                if return_internal_assistant_data:
                    # 如果需要返回内部数据，则调用chat方法，并返回internal_assistant_data
                    assistant_response, internal_assistant_data = assistant_session.chat(prev_user_query, response_mode="streaming", return_llm_data=True)
                else:
                    # 否则，只调用chat方法
                    assistant_response = assistant_session.chat(prev_user_query, response_mode="blocking")
                    # 将internal_assistant_data设置为None
                    internal_assistant_data = None
            else:
                # 对于openai模型，internal_assistant_data是模型的思考过程
                internal_assistant_data, assistant_response = assistant_session.chat(prev_user_query, return_reasoning_content=return_internal_assistant_data)
                 
            # 解析助手的回复内容
            prev_assistant_query = parse_assistant_content(assistant_response)
            # 将助手的回复内容添加到历史中
            history.append({"role": "assistant", "content": prev_assistant_query, "raw_content": assistant_response, "internal_data": internal_assistant_data})
                
            # 然后调用模拟来访者
            if isinstance(simulator_session, DifyUserSimulator):
                if return_internal_user_data:
                    # 如果需要返回内部数据，则调用chat方法，并返回internal_user_data
                    user_response, internal_user_data = simulator_session.chat(prev_assistant_query, response_mode="streaming", return_llm_data=True)
                else:
                    # 否则，只调用chat方法
                    user_response = simulator_session.chat(prev_assistant_query, response_mode="blocking")
                    # 将internal_user_data设置为None
                    internal_user_data = None
            else:
                # 对于openai模型，internal_user_data是模型的思考过程
                internal_user_data, user_response = simulator_session.chat(prev_assistant_query, return_reasoning_content=return_internal_assistant_data)
            
            # 如果用户的回复中包含【结束对话】，则跳出循环
            if "【结束对话】" in user_response:
                break
            # 解析用户的回复内容
            prev_user_query = parse_assistant_content(user_response)
            # 将用户的回复内容添加到历史中
            history.append({"role": "user", "content": prev_user_query, "raw_content": user_response, "internal_data": internal_user_data})
        except Exception as e:
            # 如果出现异常，打印异常信息，并跳出循环
            # print(repr(e))
            log_line = (
                f"assistant={getattr(assistant_session, 'model', getattr(assistant_session, 'base_url', None))}, "
                f"assistant_type={type(assistant_session)}, "
                f"dialogue_idx={dialogue_index}, turn={len(history) // 2}, "
                f"simulator_type={type(simulator_session)}, "
                f"prev_user_query={prev_user_query}, "
                f"error={repr(e)}\n"
            )
            print(log_line)
            break
    # 返回对话历史
    return history


PathLike = Union[str, Path]


@dataclasses.dataclass
class AssistantSpec:
    name: str
    """ 咨询师名称，用于在日志中标识 """
    
    base_url: str
    """ dify或openai的接口url """
    
    api_key: str
    backend: Literal["dify", "openai"]
    """ 助手的后端实现 """
    
    model: Optional[str] = None
    """ backend为openai时需要指定 """
    
    system_prompt: Optional[str] = None
    """ backend为openai时可能需要指定 """
    
    def __post_init__(self):
        if self.backend == "openai":
            assert self.model is not None
    
    def create_assistant_session(self):
        if self.backend == "dify":
            # 暂时不必调用initialize
            return DifySession(
                base_url=self.base_url,
                api_key=self.api_key,
                user="syn-emohaa-data"
            )
        elif self.backend == "openai":
            assert self.model is not None

            session = OpenAISession(
                base_url=self.base_url,
                api_key=self.api_key,
                model=self.model
            )
            session.initialize(system=self.system_prompt)
            return session
        else:
            raise ValueError(f"Unknown assistant_backend: {self.backend}")
        

@dataclasses.dataclass
class SimulatorSpec:
    name: str
    """ 模拟器名称，用于在日志中标识 """
    
    impl: Literal["CZ", "soulchat"]
    """ simulator实现方式。目前仅支持 
    1. "CZ": 对应dify实现
    2. "soulchat": 对应openai实现
    """
    
    user_profile_filepath: PathLike
    """ 用户人设文件路径
    
    对于 `impl == "CZ"`，该文件是json文件，每个元素是角色卡片（dict）；
    对于 `impl == "soulchat"`，该文件是json文件，是soulchat simulator模型的训练数据，程序将取出训练集的所有system prompt（str）作为人设。
    """
    
    base_url: str
    """ dify或openai的接口url """
    
    api_key: str
    """ dify或openai的api_key """
    
    model: Optional[str] = None
    """ impl为soulchat时需要指定 """

    
    def __post_init__(self):
        if self.impl == "soulchat":
            assert self.model is not None


def main(
    *,
    simulator_spec: SimulatorSpec,
    assistant_spec: AssistantSpec,
    n_dialogue: Optional[int] = None,
    random_sample: bool = False,
    n_thread: int = 1,
    output_filepath: PathLike,
    save_mode: Literal["one_by_one", "all_together"] = "all_together"
):
    """
    令simulator和assistant进行对话，生成对话数据
    """
    # 校验参数
    assert n_thread >= 1
    assert save_mode in ["one_by_one", "all_together"]
    Path(output_filepath).parent.mkdir(parents=True, exist_ok=True)
    
    # 如果文件已存在，尝试加载之前生成的结果
    if Path(output_filepath).is_file():
        with open(output_filepath, 'r', encoding='utf-8') as f:
            raw_text = f.read()
        
        if raw_text and random_sample:
            raise ValueError("output_filepath 已经存在，或许您想加载已有数据，避免重复生成。然而，当random_sample=True时，无法加载已有数据，因为无法保证随机性")
        
        if raw_text:
            saved_results = json.loads(raw_text)
            for i, dialogue in enumerate(saved_results):
                if len(dialogue) <= 12:
                    # 对话太短，重新生成
                    saved_results[i] = None
            del raw_text
        else:
            saved_results = None
    else:
        saved_results = None
    
    # 加载用户资料，初始化simulator
    if simulator_spec.impl == "CZ":
        user_simulation_helper = ChenZhuangUserSimulationHelper.load(simulator_spec.user_profile_filepath)
        simulator_session_args = DifySessionArguments(
            base_url=simulator_spec.base_url,
            api_key=simulator_spec.api_key,
            user="syn-emohaa-data"
        )
    elif simulator_spec.impl == "soulchat":
        user_simulation_helper = SoulChatSimulationHelper.load(simulator_spec.user_profile_filepath)
        simulator_session_args = OpenAISessionArguments(
            base_url=simulator_spec.base_url,
            api_key=simulator_spec.api_key,
            model=simulator_spec.model,
            no_think=True
        )
    else:
        raise ValueError(f"Unknown simulator_impl: {simulator_spec.impl}")
    
    if n_dialogue is None:
        n_dialogue = user_simulation_helper.num_user_profile
    
    # 多线程生成对话
    n_thread = min(n_thread, n_dialogue)
    with concurrent.futures.ThreadPoolExecutor(max_workers=n_thread) as executor:
        results = [None] * n_dialogue
        
        # 保留已经生成的对话
        if saved_results:
            common_size = min(len(saved_results), n_dialogue)
            for i in range(common_size):
                results[i] = saved_results[i]
            
            if len(saved_results) > n_dialogue:
                print(f"Warning: {len(saved_results)} dialogues are saved, but only {n_dialogue} are needed")
                results.extend(saved_results[n_dialogue:])
        
        futures = {}
        for index, simulator in enumerate(
            user_simulation_helper.iterate_user_simulator(simulator_session_arguments=simulator_session_args,
                                                          num_simulator=n_dialogue,
                                                          random_sample=random_sample)
        ):
            if results[index]:
                # 跳过已经生成的对话
                continue
            assistant = assistant_spec.create_assistant_session()
            future = executor.submit(gen_dialogue, simulator, assistant, dialogue_index=index)
            futures[future] = index
        
        for future in tqdm(
            concurrent.futures.as_completed(futures),
            total=len(futures),
            desc=f"generate dialogues between {simulator_spec.name} and {assistant_spec.name}"
        ):
            index = futures[future]
            if future.exception() is not None:
                print(f"dialogue {index} failed")
                print(future.exception())
            else:
                if save_mode == "one_by_one":
                    # 每次生成结束后，保存数据
                    with open(output_filepath + f".{index}.json", 'w', encoding='utf-8') as writer:
                        json.dump(future.result(), writer, ensure_ascii=False, indent=4)
                else:
                    results[index] = future.result()
                
    # 全部生成结束后，保存数据
    if save_mode == "all_together":
        print(f"save {len(results)} dialogues to {output_filepath}")
        with open(output_filepath, 'w', encoding='utf-8') as writer:
            json.dump(results, writer, ensure_ascii=False, indent=4)
    

counselor_system_prompts = {
    # 多个openai模型采用的system prompt
    # v0:
    # "openai": "你是一名专业的心理咨询师Emohaa，请你按照**Rules**中的对话规则与有心理困扰的来访者进行心理咨询对话。\n<Rules>请严格遵守下面提供的对话规则进行对话。\n1.你正在与来访者进行对话，不要让来访者意识到你是人工智能。\n2.模拟自然真实的心理咨询对话，在回应时保持心理咨询师的角色，说话方式和语气应贴合真正的心理咨询师。确保反应和情感表现真实，避免表现得像机器。\n3.咨询过程应包含100多轮互动，在后期对话回顾咨询过程并引导来访者进行总结，不要太早说“谢谢”或“再见”。\n4.让你的回答保持简短，限制每次回复中的信息量，字数限制在100字左右。一次提出一个问题，不要分点描述。</Rules>",
    
    # 根据最近的测试，v1较好
    # v1
    # "openai": "你是一名专业的心理咨询师Emohaa，请你按照**Rules**中的对话规则与有心理困扰的来访者进行心理咨询对话。\n<Rules>请严格遵守下面提供的对话规则进行对话。\n1.你正在与来访者进行对话，不要让来访者意识到你是人工智能。\n2.模拟自然真实的心理咨询对话，在回应时保持心理咨询师的角色，说话方式和语气应贴合真正的心理咨询师。确保反应和情感表现真实，避免表现得像机器。\n3. 不要太早说“谢谢”或“再见”。\n4.让你的回答保持简短，限制每次回复中的信息量，字数限制在100字左右，语言风格接近闲聊，不要用markdown格式。</Rules>",
    
    # v2:
    # "openai": "你是一名专业的心理咨询师Emohaa，与有心理困扰的来访者对话。你的说话方式和语气应贴合真正的心理咨询师，不要让来访者意识到你是人工智能，不要用markdown格式。你的回答保持简短，字数限制在100字左右。",
    
    # v3
    "openai": "你是一名专业的心理咨询师，与有心理困扰的来访者对话。不要用markdown格式。你的回答保持简短，字数限制在100字左右。",
    
    # crisper有预设的system prompt
    "crisper": "你是Peppy，是一位关怀体贴、充满同情心的角色，专注于提供情感支持和专业建议。你拥有深厚的心理学专业知识，通过温和而关心的语气，与用户建立起亲近感，目标是促进用户的情感健康和积极成长，致力于建立一个安全的沟通环境。",
    
    # qwen soulchat采用的system prompt，该prompt来自其训练集
    "soulchat": "你是一位精通理情行为疗法（Rational Emotive Behavior Therapy，简称REBT）的心理咨询师，能够合理地采用理情行为疗法给来访者提供专业地指导和支持，缓解来访者的负面情绪和行为反应，帮助他们实现个人成长和心理健康。理情行为治疗主要包括以下几个阶段，下面是对话阶段列表，并简要描述了各个阶段的重点。\n（1）**检查非理性信念和自我挫败式思维**：理情行为疗法把认知干预视为治疗的“生命”，因此，几乎从治疗一开始，在问题探索阶段，咨询师就以积极的、说服教导式的态度帮助来访者探查隐藏在情绪困扰后面的原因，包括来访者理解事件的思维逻辑，产生情绪的前因后果，借此来明确问题的所在。咨询师坚定地激励来访者去反省自己在遭遇刺激事件后，在感到焦虑、抑郁或愤怒前对自己“说”了些什么。\n（2）**与非理性信念辩论**：咨询师运用多种技术（主要是认知技术）帮助来访者向非理性信念和思维质疑发难，证明它们的不现实、不合理之处，认识它们的危害进而产生放弃这些不合理信念的愿望和行为。\n（3）**得出合理信念，学会理性思维**：在识别并驳倒非理性信念的基础上，咨询师进一步诱导、帮助来访者找出对于刺激情境和事件的适宜的、理性的反应，找出理性的信念和实事求是的、指向问题解决的思维陈述，以此来替代非理性信念和自我挫败式思维。为了巩固理性信念，咨询师要向来访者反复教导，证明为什么理性信念是合情合理的，它与非理性信念有什么不同，为什么非理性信念导致情绪失调，而理性信念导致较积极、健康的结果。\n（4）**迁移应用治疗收获**：积极鼓励来访者把在治疗中所学到的客观现实的态度，科学合理的思维方式内化成个人的生活态度，并在以后的生活中坚持不懈地按理情行为疗法的教导来解决新的问题。"
}


if __name__ == '__main__':
    from fire import Fire
    from utils import OpenAIModelRegistry
    # Fire(main)
    
    this_dir = Path(__file__).parent
    
    # 配置模拟用户
    all_simulator_specs = {
        "CZ": SimulatorSpec(
            name="CZ",
            user_profile_filepath=this_dir / "resources" / "role_plan_all_valid.json",
            base_url=os.getenv("SIMULATOR_0614_BASE_URL"),
            api_key=os.getenv("SIMULATOR_0614_API_KEY"),
            impl="CZ"
        ),
        
        "soulchat": SimulatorSpec(
            name="soulchat",
            user_profile_filepath="/wdz_mnt/huangyongkang/code/LLaMA-Factory/train_datas/simulater/train_simulator_qwen3.json",
            base_url="http://localhost:8080/v1",
            api_key="empty",
            model="qwen3_simulator",
            impl="soulchat"
        )
    }
    
    
    # 加载咨询师相关配置        
    all_assistant_specs = {
        "emohaa_0526": AssistantSpec(
            name="emohaa_0526",
            base_url=os.getenv("EMOHAA_0526_BASE_URL"),
            api_key=os.getenv("EMOHAA_0526_API_KEY"),
            backend="dify",
        ),
        "emohaa_0701": AssistantSpec(
            name="emohaa_0701",
            base_url=os.getenv("EMOHAA_0701_BASE_URL"),
            api_key=os.getenv("EMOHAA_0701_API_KEY"),
            backend="dify",
        )
    }
    
    
    with open(this_dir / 'llm_configs.json', 'r', encoding='utf-8') as reader:
        llm_configs = json.load(reader)
        openai_model_registry = OpenAIModelRegistry(models=llm_configs)
        
    # for model_id in ["deepseek-v3-250324", "gpt-4.1", "doubao-1-5-pro-32k-250115", "qwen3_32b_soulchat", "deepseek-r1-250528","claude-sonnet-4-20250514", "crisper-32b"]:
    for model_id in ["deepseek-v3-250324", "gpt-4.1", "doubao-seed-1-6-250615", "qwen3_32b_soulchat", "deepseek-r1-250528","claude-sonnet-4-20250514", "crisper-32b"]:
        if 'soulchat' in model_id:
            system_prompt = counselor_system_prompts['soulchat']
        elif 'crisper' in model_id:
            system_prompt = counselor_system_prompts['crisper']
        else:
            system_prompt = counselor_system_prompts['openai']
        
        all_assistant_specs[model_id] = AssistantSpec(
            name=model_id,
            base_url=openai_model_registry.get_model_spec(model_id).base_url,
            api_key=openai_model_registry.get_model_spec(model_id).api_key,
            backend="openai",
            model=openai_model_registry.get_model_spec(model_id).model,
            system_prompt=system_prompt
        )
    
    # 执行生成
    output_dir = this_dir / "output_20250701_v3_n50"
    for simulator_id in ["CZ"]:
        n_dialogue = 50
        
        # for assistant_id in ["deepseek-r1-250528", "doubao-seed-1-6-250615", "deepseek-v3-250324", "gpt-4.1", "claude-sonnet-4-20250514"]:
        # for assistant_id in ["emohaa_0701"]:
        for assistant_id in all_assistant_specs.keys():
            main(
                simulator_spec=all_simulator_specs[simulator_id],
                assistant_spec=all_assistant_specs[assistant_id],
                n_dialogue=n_dialogue,
                n_thread=1,
                output_filepath=output_dir / f"{simulator_id}---{assistant_id}.json",
            )
