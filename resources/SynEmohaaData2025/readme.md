## 简介
此项目实现模拟来访者与咨询师的交互对话。
部分资源文件并未通过git维护，而是放在飞书文档中 <https://zhipu-ai.feishu.cn/wiki/GJnAwk48biCSDbkhDX3cDTpMnLf?from=from_copylink> ，目的是
1. 避免git历史记录api_key
2. 通过飞书文档控制权限

## 依赖
```bash
pip install -r requirements.txt
```

## 文件说明
* .env: 通过环境变量保存 Emohaa(dify) 和 模拟来访者(dify) 的base_url和api_key。该文件不会被git记录，请在飞书文档下载并放置此项目目录下（与readme.md同一目录）
* llm_configs.json: 通过配置文件保存各个claude、deepseek等模型接口的base_url和api_key。该文件不会被git记录，请在飞书文档下载并放置此项目目录下（与readme.md同一目录）
* dify_client.py: 封装dify接口
* openai_client.py: 封装openai接口，提供与`DifySession`类似的OpenAISession
* user_simulator.py: 基于openai或者dify api的模拟用户。本模块辅助实现用户人设的加载、模拟用户的初始化
* utils.py: 辅助函数
* gen_dial.py: 调用模拟来访者和咨询师，实现交互对话
* add_turn_range.py: 为生成的对话添加每个轮次的plan标注。plan标注来自于模拟来访者的人设。

其他细节
* `openai_client.py`和`user_simulator.py`会封装api调用细节，提供Session接口（DifySession和OpenAISession）。Session接口可以简化交互对话逻辑的实现。
* 由于项目支持多个模拟来访者和咨询师，`gen_dial.py`会先配置好所有模拟来访者和咨询师的参数（base_url, api_key等），再执行交互对话。
* 每次交互对话，程序会选择（迭代或者采样）一个用户人设，并分别初始化一个模拟来访者的Session和咨询师的Session。Session可以视为与来访者/咨询师对话的接口，`gen_dial.gen_dialogue`函数通过转发来访者/咨询师的回复实现交互对话。由于来访者模型不能直接生成greeting，程序会选择一个预设的greeting，作为第1轮的来访者回复，再交替调用咨询师和来访者api。
* 整个项目是同步逻辑，不包含异步（async/await）逻辑。
* 由于`Session`接口本身有状态，单个`Session`对象不支持多线程调用。`gen_dial.py`通过创建大量Session，实现并发的进行多个对话。


## 项目细节
### api
本项目主要支持`dify`和`openai` 2种api，分别通过`dify_client.py`和`openai_client.py`封装出Session，简化交互对话程序的编写。

### 咨询师
1. Emohaa
基于dify实现，其base_url和api_key存储在`.env`文件中

2. soulchat2.0
基于开源数据集(https://modelscope.cn/datasets/YIRONGCHEN/PsyDTCorpus/summary)的助手回复训练的qwen3 32b。
需要用vllm部署，然后此项目通过openai接口调用。其system prompt来自训练数据，每次对话时采用**固定的system prompt**。

部署的相关细节见飞书文档。

3. gpt/claude/deepseek等
需要将调用云平台提供的api的base_url和api_key整理在`llm_configs.json`中，然后此项目通过openai接口调用。其system prompt是人工编写的。

调用时，采用标准openai接口。即messages中第1条消息是system，描述咨询师的人设和行为。messages中的其他多条消息是user/assistant交替，对应来访者（user）和咨询师（assistant）的对话历史。

### 模拟来访者
1. 标准病人 (SP) v1.2-0614
ChenZhuang基于dify实现的模拟来访者。对话开始前，需要传入一个角色卡片（dict）。

2. soulchat2.0
基于开源数据集(https://modelscope.cn/datasets/YIRONGCHEN/PsyDTCorpus/summary)的用户回复训练的qwen3 32b。
需要用vllm部署，然后此项目通过openai接口调用。其system prompt来自训练数据，每次对话时采用**不同的system prompt**，system prompt描述了用户的人设和行为。

部署的相关细节见飞书文档。

每次对话前，需要从它的训练集中采样一个system prompt，作为soulchat simulator模型的system prompt。训练集的system prompt就是soulchat simulator的角色人设。其训练集见飞书文档。


