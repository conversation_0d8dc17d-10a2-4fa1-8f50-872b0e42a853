"""
对于ChenZhuang simulator产出的对话，为每个轮次添加维度标签
"""
import json
import re
from typing import Union
from pathlib import Path
from functools import lru_cache
from copy import deepcopy


from user_simulator import ChenZhuangUserSimulationHelper


@lru_cache(maxsize=1)
def get_user_profiles():
    this_dir = Path(__file__).parent
    user_profiles = ChenZhuangUserSimulationHelper.load(
        this_dir / "resources" / "role_plan_all_valid.json"
    ).user_profiles
    
    return [json.loads(item['patient_info_input']) for item in user_profiles]



def main(
    dialogue_file: Union[str, Path],
    output_file: Union[str, Path]
):
    with open(dialogue_file, 'r', encoding='utf-8') as reader:
        dialogues = json.load(reader)
    
    user_profiles = deepcopy(get_user_profiles())
    
    
    assert len(dialogues) <= len(user_profiles)
    
    for dialogue, user_profile in zip(dialogues, user_profiles):
        if len(dialogue) <= 3:
            continue

        for key in user_profile['dialogue_plan']:
            pattern = re.compile(r"(\d+)-(\d+)")
            match = pattern.fullmatch(key)
            assert match is not None
            start, end = map(int, match.groups())
            # start和end从1开始计数，闭区间[start, end]
            assert start <= end
            
            # start和end从0开始计数，左闭右开[start, end)
            for turn_idx in range(start-1, end):
                idx = turn_idx * 2 + 1
                if 0 <= idx < len(dialogue):
                    dialogue[idx]['plan'] = user_profile['dialogue_plan'][key]
                
    print(f"save to {output_file}")
    Path(output_file).parent.mkdir(parents=True, exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as writer:
        json.dump(dialogues, writer, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    this_dir = Path(__file__).parent
    
    dialogue_dir = this_dir / 'output_20250701_v3_n50'
    output_dir = this_dir / 'output_20250701_v3_n50_with_label'
    
    for dialogue_file in dialogue_dir.glob('*.json'):
        output_file = output_dir / dialogue_file.name
        main(
            dialogue_file,
            output_file
        )

