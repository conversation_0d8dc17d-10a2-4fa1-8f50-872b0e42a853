"""
更加hign-level的模拟来访者配置
* LLM & 解码参数
* prompt
* 人设
"""
import json
import random
import dataclasses
from functools import lru_cache
from pathlib import Path
from typing import Union, Optional, Generator

import yaml
from pydantic import BaseModel
from openai_client import OpenAISessionArguments
from .prompt_engineering import PromptTemplate
from user_simulator import OpenAIUserSimulator, UserSimulationHelper, Persona


this_dir = Path(__file__).parent
resources_dir = this_dir / 'resources'


def safe_load_yaml(file_path):
    """
    安全地加载 YAML 文件，避免执行潜在的恶意代码。
    
    参数:
        file_path (str): YAML 文件的路径。
    
    返回:
        dict: 加载的 YAML 数据。
    """
    # 使用 yaml.safe_load 来防止加载不安全的内容
    with open(file_path, 'r', encoding='utf-8') as file:
        data = yaml.safe_load(file)
    return data


@lru_cache()
def core_drive_mapping():
    data = safe_load_yaml(resources_dir / 'universal_core_drives.yaml')
    
    core_drive = {}
    for item in data:
        key = item['name']
        assert key not in core_drive
        core_drive[key] = item['name'] + '：' + item['description']
    return core_drive


@lru_cache()
def reaction_pattern_mapping():
    data = safe_load_yaml(resources_dir / 'universal_reaction_patterns.yaml')
    
    reaction_pattern = {}
    for item in data:
        key = item['name']
        assert key not in reaction_pattern
        reaction_pattern[key] = item['name'] + '：' + item['description']
    return reaction_pattern


def replace_field(persona: dict):
    core_drive_key = "Core Drive"
    reaction_pattern_key = "Reaction Pattern"
    
    assert core_drive_key in persona
    persona[core_drive_key] = core_drive_mapping()[persona[core_drive_key]]
    
    assert reaction_pattern_key in persona
    persona[reaction_pattern_key] = reaction_pattern_mapping()[persona[reaction_pattern_key]]


@dataclasses.dataclass
class PerSimulatorConfig:
    """每个模拟来访者的配置"""
    session_arguments: OpenAISessionArguments
    system_prompt_template: PromptTemplate
    
    name: str = ""
    """ 配置名 """

    def make_user_simulator(self, persona: Union[dict, Persona], add_details: bool = False):
        """生成一个模拟来访者的实例"""
        simulator = OpenAIUserSimulator(
            **dataclasses.asdict(self.session_arguments)
        )
        
        if isinstance(persona, BaseModel):
            persona = persona.model_dump(exclude=["persona_id"])
        
        if add_details:
            replace_field(persona)
            
        system_prompt = self.system_prompt_template.get_prompt(persona=persona)
        simulator.initialize(system_prompt)
        return simulator


class OpenAISimulationHelper(UserSimulationHelper[Persona]):
    """
    固定LLM和system prompt模板，用多个persona构造模拟来访者
    """
    
    @classmethod
    def load(cls, role_card_filepath: str):
        with open(role_card_filepath, 'r', encoding='utf-8') as reader:
            data = json.load(reader)
        
        personas = [Persona.model_validate(item) for item in data]
        return cls(personas)

    def iterate_user_simulator(self,
                               per_simulator_config: PerSimulatorConfig,
                               num_simulator: Optional[int] = None,
                               random_sample: bool = False,
                               add_details: bool = False
                               ) -> Generator[OpenAIUserSimulator, None, None]:
        """
        迭代地生成用户模拟器
        
        Args:
            per_simulator_config (PerSimulatorConfig): openai session的参数，及系统提示模板
            num_simulator (Optional[int], optional): 模拟器的数量. Defaults to None. 若为None，则生成所有用户模拟器
            random_sample (bool, optional): 是否随机采样. Defaults to False.
        
        """
        if num_simulator is not None:
            assert num_simulator <= self.num_user_profile
            if random_sample:
                user_profile_indices = random.sample(range(self.num_user_profile), num_simulator)
            else:
                user_profile_indices = range(num_simulator)
        else:
            user_profile_indices = range(self.num_user_profile)
        
        for index in user_profile_indices:
            persona = self.user_profiles[index]
            
            simulator = per_simulator_config.make_user_simulator(persona=persona, add_details=add_details)
            yield simulator



