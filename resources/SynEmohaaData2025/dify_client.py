import json
import dataclasses
from typing import Optional, Dict, Any, Literal
from copy import deepcopy

import requests


# 注意：下列函数恰好能解析dify返回的sse，
# 但不一定能解析所有符合sse规范的响应。
# 更规范的解析代码可以参考 https://github.com/mpetazzoni/sseclient/blob/master/sseclient/__init__.py
def parse_sse(stream):
    for line in stream:
        if not line.strip():
            continue
        if isinstance(line, bytes):
            line = line.decode("utf-8")
        
        if line.startswith("data:"):
            obj = json.loads(line[5:].strip())
            yield obj


def request_dify_chatflow(
    base_url: str,
    api_key: str,
    query: str,
    *,
    conversation_id: str = "",
    inputs: Optional[Dict[str, Any]] = None,
    user: str,
    response_mode: Literal["streaming", "blocking"],
    timeout: Optional[int] = 180
):
    """
    inputs参数对应workflow的开始节点。低版本dify中，只有创建新session时传入的inputs会生效, 后续的inputs会被忽略.
    """
    assert response_mode in ("streaming", "blocking")
    url = base_url + "/chat-messages"
    
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    

    body = {
        "inputs": inputs if inputs is not None else {},
        "query": query,
        "response_mode": response_mode,
        "conversation_id": conversation_id,
        "user": user,
        "files": []
    }
    
    response = requests.post(
        url=url,
        json=body,
        headers=headers,
        stream=(response_mode == "streaming"),
        timeout=timeout
    )
    response.raise_for_status()
    
    if response_mode == "blocking":
        return response.json()
    else:
        return parse_sse(
            response.iter_lines()
        )


@dataclasses.dataclass
class DifySessionArguments:
    base_url: str
    api_key: str
    user: str



class DifySession:
    """
    维护与dify chatflow的会话
    
    References: https://docs.dify.ai/api-reference/%E5%AF%B9%E8%AF%9D%E6%B6%88%E6%81%AF/%E5%8F%91%E9%80%81%E5%AF%B9%E8%AF%9D%E6%B6%88%E6%81%AF
    """
    def __init__(self, base_url: str, api_key: str, user: str, timeout=180):
        self.base_url = base_url
        self.api_key = api_key
        self.user = user
        self.conversation_id = ""
        self.dialogue = []
        self.inputs = None
        self.timeout = timeout
    
    def initialize(self, inputs: Optional[Dict[str, Any]] = None):
        self.conversation_id = ""
        self.dialogue = []
        self.inputs = inputs
    
    def chat(self,
             query: str, 
             inputs: Optional[Dict[str, Any]] = None,
             response_mode: Literal["streaming", "blocking"] = "blocking",
             return_llm_data: Optional[bool] = None):
        if inputs is None and self.inputs is not None:
            inputs = self.inputs
        
        response = request_dify_chatflow(
            base_url=self.base_url,
            api_key=self.api_key,
            query=query,
            conversation_id=self.conversation_id,
            inputs=inputs,
            user=self.user,
            response_mode=response_mode,
            timeout=self.timeout
        )
        if response_mode == "blocking":
            conversation_id = response['conversation_id']
            answer = response["answer"]
            llm_data = []
        else:
            chunks = list(response)
            answer = "".join(chunk['answer'] for chunk in chunks if chunk['event'] == "message")
            
            conversation_id: str = ""
            for chunk in chunks:
                if chunk.get("conversation_id"):
                    conversation_id = chunk["conversation_id"]
                    break
            
            # 解析中间LLM的输入、输出
            llm_data = []
            for chunk in chunks:
                if "data" in chunk and chunk['data'].get("node_type") == "llm" and chunk['data'].get("process_data"):
                    inputs = deepcopy(chunk['data']['process_data'])
                    messages = inputs.pop("prompts")
                    for msg in messages:
                        msg['content'] = msg.pop("text")
                        msg.pop("files", None)
                    inputs['messages'] = messages
                    inputs['title'] = chunk['data']['title']
                    inputs['output'] = chunk['data']['outputs']['text']
                    llm_data.append(inputs)

        if self.conversation_id:
            assert self.conversation_id == conversation_id
        self.conversation_id = conversation_id
        self.dialogue.append((query, answer))
        
        if return_llm_data is None:
            return_llm_data = (response_mode == "streaming")
        
        if return_llm_data:
            return answer, llm_data
        else:
            return answer
