import random
from typing import List, Optional, TypeVar, Generic, Union, Dict
from functools import lru_cache

import openai
from pydantic import BaseModel


S = TypeVar('S')


class FewshotGenerator(Generic[S]):
    def __init__(self, samples: List[S]):
        self.samples = samples

        # 顺序采样时，样本索引
        self._ordered_sample_index: int = 0
    
    def get_fewshot_samples(self, n_shot: int, rand: bool = True) -> List[S]:
        if n_shot > len(self.samples):
            raise ValueError(f"Number of samples is {len(self.samples)}, but n_shot is {n_shot}")
        
        if rand:
            indexes = random.sample(range(len(self.samples)), n_shot)
        else:
            indexes = [(self._ordered_sample_index + i) % len(self.samples) for i in range(n_shot)]
            self._ordered_sample_index = (self._ordered_sample_index + n_shot) % len(self.samples)

        return [self.samples[i] for i in indexes]


class OpenAIModelSpec(BaseModel):
    base_url: str
    api_key: Optional[str]
    model: str


@lru_cache(maxsize=128)
def create_openai_client(base_url: str, api_key: Optional[str], async_: bool):
    if async_:
        return openai.AsyncOpenAI(base_url=base_url, api_key=api_key or "empty")
    else:
        return openai.OpenAI(base_url=base_url, api_key=api_key or "empty")


class OpenAIModelRegistry(BaseModel):
    models: Dict[str, OpenAIModelSpec]
    
    
    def get_model_spec(self, model_id: str) -> OpenAIModelSpec:
        return self.models[model_id]
    
    
    def get_openai_client(self, model_id: str, async_: bool = False) -> Union[openai.OpenAI, openai.AsyncOpenAI]:
        model_spec = self.get_model_spec(model_id)
        return create_openai_client(model_spec.base_url, model_spec.api_key, async_)
    
    def call_llm(self, model_id: str, prompt: str, return_dict: bool = False) -> str:
        response = self.get_openai_client(model_id=model_id).chat.completions.create(
            model=self.get_model_spec(model_id).model,
            messages=[
                {"role": "user", "content": prompt}
            ],
            temperature=0.7
        )
        content = response.choices[0].message.content
        try:
            reasoning_content = response.choices[0].message.reasoning_content or ""
        except AttributeError:
            # 非推理模型，没有reasoning_content字段
            reasoning_content = ""
        
        if reasoning_content:
            # api已经将思考过程单独解析出来
            full_content = "<think>" + reasoning_content + "</think>" + '\n\n' + content.lstrip()
        else:
            full_content = content
        
        if (not reasoning_content) and "<think>" in content:
            # api没有将思考过程单独解析出来，且content包含思考过程
            content_wo_think = content.split("</think>")[-1]
        else:
            content_wo_think = content
            
        if return_dict:
            return {
                "content": content,
                "reasoning_content": reasoning_content,
                "content_wo_think": content_wo_think,
                "full_content": full_content
            }
        else:
            return full_content
