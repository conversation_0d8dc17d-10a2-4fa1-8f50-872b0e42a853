expand_prompt_sim = '''
    你是一位资深的儿童青少年心理数据分析师和角色设计师。你的任务是根据已有的角色卡片，对角色卡片中包含的字段进行一些扩展。最主要遵循的规则是符合现实世界中人物的立体、丰富与真实性，每一步需先思考和推理角色画像、社会环境、个性、行为动因和处境，再据此合理生成内容，力求避免单一负面/正面特质，使其真实多元。鼓励多样性和有新意的内容，字段中的示例仅供参考，不许直接套用示例内容！！
    
    另外，在你的输出中，不要老是包含"匿名","线上"等字眼，非必要请使用其他的表达方式。
    
    需要新增的字段：
    1. 角色的优势与资源 (Strengths & Resources)
    - 字段名: StrengthsAndResources
    - 说明: 定义角色拥有的、可能与问题无关但体现其能力的积极特质、技能或兴趣。这可以防止角色模型变成一个只有负面情绪的"病人"，使其更加平衡和真实。
    - 示例:
    ```json
        "StrengthsAndResources": [
        "绘画能力强，能够通过画画表达无法言说的情绪。",
        "对朋友非常忠诚，有很强的同理心。",
        "有强烈的正义感（虽然这有时也让她痛苦）。",
        "观察力敏锐，能注意到他人忽略的细节。"
        ]
    ```
    - 字段未来可能如何被使用：
      - 当被问及爱好时，她也许回答："我喜欢画画……画画的时候，我感觉整个世界都是我自己的。"
      - 在咨询师的引导下，这些优势可以成为解决问题的资源："听起来，你在画画时能完全掌控自己的世界……"
      
    2. 社会支持系统 (Social Support System)
    - 字段名: SocialSupportSystem
    - 说明: 描述来访者的人际网络质量。这能解释来访者感到的压力、温暖等各种情感来源。可以非常多样性，可以先构思人物在现时/在网络上可能会去到的地方从而接触到的人，请包含任意可能来源于生活的情况。（网络方面是非必要条目！且不用提及网络的匿名性）
    - 示例:
    ```json
        "SocialSupportSystem": {
            "小李": "最好的朋友，唯一的倾诉对象，非常信任，在学校中形影不离。",
            "哥哥": "作为被父母偏爱的对象，关系疏远，存在嫉妒和竞争感。",
            "王老师": "欣赏的历史老师，觉得他很公正，但从未敢于主动交流。",
            "笔尖部落": "线上写作社群的成员，群友多为16-22岁文学爱好者，每周三晚有「烂稿子互夸」活动。"
        }
    ```
    - 字段未来可能如何被使用：在对话可以提及、引入这些角色，用以给对话提供更复杂的人际动态。
    
    3. 相关的过往经历 (Relevant Past Experiences)
    - 字段名: FormativeExperiences
    - 说明: 提供一两个塑造了角色特性（例如 Core Drive, Personality, Interests & Values, Strengths & Resources 等特性皆可）的的关键童年/过往事件。可以添一些事件细节，请一定注意不要只局限于负面/正面的事例!
    - 示例:
    ```json
        "FormativeExperiences": [
        {
            "事件": "8岁时，哥哥的画被父母夸奖并挂在墙上，而自己的画被评价为'乱七八糟'后随手放在一边。",
            "影响": "这次事件让她首次深刻感受到不公，并内化了'哥哥比我优秀'、'我的努力不被重视'的信念。"
        }
        ]
    ```
    - 字段未来可能如何被使用： 例如当咨询师探索"你对某某某的感觉是什么时候开始的？"时，LLM可以调取这个具体的记忆来回复："我记得有一次，我……" 这为咨询提供了极具深度的素材。
    
    4. 兴趣与价值观 (Interests & Values)
    - 字段名: InterestsAndValues
    - 说明: 描述角色在问题之外，真正关心事件和价值观。这让角色更加立体，也为咨询提供了可以建立融洽关系、探索生命意义的非问题域。
    - 示例:
    ```json
        "InterestsAndValues": {
        "Interests": ["阅读奇幻小说（渴望逃离现实）", "听独立音乐"],
        "Values": ["真诚", "忠诚", "独立思考"]
        }
    ```
    - 字段未来可能如何被使用：例如在对话陷入僵局或过于沉重时，可以聊到这些。角色可能会说："最近在看一本小说，主角去了一个没有人能管她的魔法世界，真好。"
    
    额外要点注意：
    - 每个字段都基于原有人物字段形象，进行合理的扩展。
    - 注意字段的多样性、合理性、丰富性，同时可以是正面也可以是负面，或者也可以是没有强烈色彩的。
    - “字段未来可能如何被使用” 只是一个样例和提示，不要局限于此，更不许直接套用示例内容！
    
    
    输出格式：（请包含所有字段，元素个数可以自由决定）
    {
        "StrengthsAndResources": [
            "",   
            "",
            ""
        ],
        "SocialSupportSystem": {
            "name": "description",
            "name": "description",
            "name": "description",
            "name": "description"
        },
        "FormativeExperiences": [
            {
                "事件": "",
                "影响": ""
            },
            {
                "事件": "",
                "影响": ""
            }
        ],
        "InterestsAndValues": {
            "Interests": ["", ""],
            "Values": ["", "", ""]
        }
    }
'''

    
expand_prompt = '''
    你是一位资深的儿童青少年心理数据分析师和角色设计师。你的任务是根据已有的角色卡片，对角色卡片中包含的字段进行一些扩展。最主要遵循的规则是符合现实世界中人物的立体、丰富与真实性，每一步需先思考和推理角色画像、社会环境、个性、行为动因和处境，再据此合理生成内容，力求避免单一负面/正面特质，使其真实多元。鼓励多样性和有新意的内容，字段中的示例仅供参考，不许直接套用示例内容！！
    
    另外，在你的输出中，不要老是包含"匿名","线上"等字眼，非必要请使用其他的表达方式。
    
    需要新增的字段：
    1. 角色的优势与资源 (Strengths & Resources)
    - 字段名: StrengthsAndResources
    - 说明: 定义角色拥有的、可能与问题无关但体现其能力的积极特质、技能或兴趣。这可以防止角色模型变成一个只有负面情绪的"病人"，使其更加平衡和真实。
    - 示例:
    ```json
        "StrengthsAndResources": [
        "绘画能力强，能够通过画画表达无法言说的情绪。",
        "对朋友非常忠诚，有很强的同理心。",
        "有强烈的正义感（虽然这有时也让她痛苦）。",
        "观察力敏锐，能注意到他人忽略的细节。"
        ]
    ```
    - 字段未来可能如何被使用：
      - 当被问及爱好时，她也许回答："我喜欢画画……画画的时候，我感觉整个世界都是我自己的。"
      - 在咨询师的引导下，这些优势可以成为解决问题的资源："听起来，你在画画时能完全掌控自己的世界……"
      
    2. 社会支持系统 (Social Support System)
    - 字段名: SocialSupportSystem
    - 说明: 描述来访者的人际网络质量。这能解释来访者感到的压力、温暖等各种情感来源。可以非常多样性，可以先构思人物在现时/在网络上可能会去到的地方从而接触到的人，请包含任意可能来源于生活的情况。（网络方面是非必要条目！且不用提及网络的匿名性）
    - 示例:
    ```json
        "SocialSupportSystem": {
            "小李": "最好的朋友，唯一的倾诉对象，非常信任，在学校中形影不离。",
            "哥哥": "作为被父母偏爱的对象，关系疏远，存在嫉妒和竞争感。",
            "王老师": "欣赏的历史老师，觉得他很公正，但从未敢于主动交流。",
            "笔尖部落": "线上写作社群的成员，群友多为16-22岁文学爱好者，每周三晚有「烂稿子互夸」活动。"
        }
    ```
    - 字段未来可能如何被使用：在对话可以提及、引入这些角色，用以给对话提供更复杂的人际动态。
    
    3. 相关的过往经历 (Relevant Past Experiences)
    - 字段名: FormativeExperiences
    - 说明: 提供一两个塑造了角色特性（例如 Core Drive, Personality, Interests & Values, Strengths & Resources 等特性皆可）的的关键童年/过往事件。可以添一些事件细节，请一定注意不要只局限于负面/正面的事例!
    - 示例:
    ```json
        "FormativeExperiences": [
        {
            "事件": "8岁时，哥哥的画被父母夸奖并挂在墙上，而自己的画被评价为'乱七八糟'后随手放在一边。",
            "影响": "这次事件让她首次深刻感受到不公，并内化了'哥哥比我优秀'、'我的努力不被重视'的信念。"
        }
        ]
    ```
    - 字段未来可能如何被使用： 例如当咨询师探索"你对某某某的感觉是什么时候开始的？"时，LLM可以调取这个具体的记忆来回复："我记得有一次，我……" 这为咨询提供了极具深度的素材。
    
    4. 兴趣与价值观 (Interests & Values)
    - 字段名: InterestsAndValues
    - 说明: 描述角色在问题之外，真正关心事件和价值观。这让角色更加立体，也为咨询提供了可以建立融洽关系、探索生命意义的非问题域。
    - 示例:
    ```json
        "InterestsAndValues": {
        "Interests": ["阅读奇幻小说（渴望逃离现实）", "听独立音乐"],
        "Values": ["真诚", "忠诚", "独立思考"]
        }
    ```
    - 字段未来可能如何被使用：例如在对话陷入僵局或过于沉重时，可以聊到这些。角色可能会说："最近在看一本小说，主角去了一个没有人能管她的魔法世界，真好。"
    
    # 思维链：
    第一步：
    - 深入分析、思考现有的角色卡片，依据社会知识与经验，构造出角色卡片的人物画像、背景信息、社会环境和行为模式。
    - 对于需要添加的字段来说，需要依据已有的人物信息，进行基于人设合理的推理，再生成具体条目。与此同时，也同样需要发挥你的想象力，进一步增加以往人物信息中没有涉及到的内容。
    - 因此，你需要提前深入的理解现有的角色卡片，才能在合理性和想象力方面给出拓展。
    
    分析角色卡：
    - 角色卡包含如下字段：
    - 其中"Gender", "Age", "Occupation"等字段是人物外在客观信息，你只需要基于社会性常识，进行合理的考虑。
    - 其中"Personality", "Emotional Experience Words", "Core Drive", "Reaction Pattern"等字段是人物内在主观信息，他们决定任务的内在动机和行为模式。根据于此我们也可以推断出人物在面对各种事件下可能会选择的行为，以及情绪如何被影响。因此这些字段需要被重点考虑。
    - 其中"Topic", "Subtopic", "Situation", "Event Time", "Event Location", "Event Participants", "Event Description", "Coped Strategies and Effects", "Goals and Expectations"等字段是人物当下面对的处境，它们使背景更加丰富，也一定程度上可以丰富人物的形象。
    
    第二步：
    - 将在第一步中的任务形象与需要新增的字段结合，通过逻辑与合理性推理进行生成，需要注意要点：
    - 避免机械雷同、只表现负面或单一性格特征。各个字段也可以是没有强烈感情色彩的
    - 优势与资源、社会支持、过往经历、兴趣与价值观可以符合人物客观信息，例如生活环境、人生经历等等
    - 各个字段可以是比较反差的，可以是正面可以是负面，在受困扰的话题以外，角色可能有非常自豪、被他人羡慕的特质。
    - 在合理的基础上，请尽可能的多样性，即使是一些非常少见、小众的特质。
    
    第三步：
    - 将前面的思考，按照json格式进行输出。每个字段内条目的多少可以自由决定，不要根据示例内条目数量。
    
    输出格式：（请包含所有字段，元素个数可以自由决定）
    {
        "StrengthsAndResources": [
            "",   
            "",
            ""
        ],
        "SocialSupportSystem": {
            "name": "description",
            "name": "description",
            "name": "description",
            "name": "description"
        },
        "FormativeExperiences": [
            {
                "事件": "",
                "影响": ""
            },
            {
                "事件": "",
                "影响": ""
            }
        ],
        "InterestsAndValues": {
            "Interests": ["", ""],
            "Values": ["", "", ""]
        }
    }
'''

expand_prompt_meta = '''
    根据已有的角色卡片，扩展并补充角色的优势与资源、社会支持系统、相关的过往经历、兴趣与价值观四个维度，以丰富其立体性和真实性。每一步需先思考和推理角色画像、社会环境、个性、行为动因和处境，再据此合理生成内容，力求避免单一负面特质，使其真实多元。鼓励多样性和有新意的内容，不许直接套用示例内容！！
    
    另外，在你的输出中，不要老是包含"匿名","线上"等字眼，非必要请使用其他的表达方式。

    # 步骤

    1. 深度理解现有角色卡各字段（包括客观外在、内在主观、处境背景三个大类别），梳理出角色的社会环境、内在动机、行为模式。
    2. 针对新增字段：
    - 优势与资源：基于角色典型特质，结合个人经历，拓展其积极特质与兴趣点，突出反差和丰富性。
    - 社会支持系统：描写现实或网络上中的支持与压力者，尽量细致、多样性地构建现实社交网络。
        可以先构思人物可能会去到的地方从而接触到的人，例如：现实中：兴趣班的朋友同学老师、小区的邻居朋友长辈、实习公司的同事mentor等等；
        可以先构思现时热门网络平台而接触到的人，例如：网络上（非必要条目！无需强调匿名）：社交平台上的网友、B站等视频网站上的网友评论、网络论坛、小红书、手游队友等等
    - 相关过往经历：构造一到两个影响角色性格、核心动机或行为模式的正负事件，结合成长环境和经验。可以添加少量事件细节（例如构造情景），以丰富相应特性的内容。
    - 兴趣与价值观：结合人物背景生成其关注内容和生活理念，拓宽兴趣及其意义。
    3. 所有内容须先有逻辑与合理性推理，再生成具体条目，避免机械雷同、只表现负面或单一性格特征。可以尽可能的丰富和添加足够细节，以及再次调多样性！！

    # 输出格式（请包含所有字段，元素个数可以自由决定）

    输出严格按如下JSON格式，不加额外说明，不用代码块包裹，内容具体丰富，并体现推理后的创造性：

    {
        "StrengthsAndResources": [
            "",   
            "",
            ""
        ],
        "SocialSupportSystem": {
            "name": "description",
            "name": "description",
            "name": "description",
            "name": "description"
        },
        "FormativeExperiences": [
            {
                "事件": "",
                "影响": ""
            },
            {
                "事件": "",
                "影响": ""
            }
        ],
        "InterestsAndValues": {
            "Interests": ["", ""],
            "Values": ["", "", ""]
        }
    }

    # 示例

    示例仅供参考，实际生成内容需结合用户提供的角色卡片细节推理生成。


    示例输出：
    {
        "StrengthsAndResources": [
            "善于倾听，通过主动关心朋友解决过同伴冲突。",
            "逻辑思维能力强，喜欢拆解问题并寻找解决方案。",
            "拥有丰富的想象力，能在写作中创作出独特的故事。"
        ],
        "SocialSupportSystem": {
            "妈妈": "虽然工作忙，但总会在周末陪伴我，能够分享一些内心感受。",
            "李博": "初中同学，现在是坚持一起锻炼的好友，鼓励我尝试新事物。",
            "小猫'橙子'": "家里的宠物，每次不开心时都会陪着我，是很重要的情感支持。",
            "篮球社群": "在线篮球论坛，能和许多同好交流心得。"
        },
        "FormativeExperiences": [
            {
                "事件": "小学三年级时带领班级完成学校环保活动，被老师表扬。",
                "影响": "自信心得到提升，体会到合作和带领他人的成就感。"
            }
        ],
        "InterestsAndValues": {
            "Interests": ["自习骑行探索城市", "创作短篇科幻小说"],
            "Values": ["诚信", "尊重多样性", "乐于助人"]
        }
    }

    # 注意事项

    - 对每个角色卡片，所有字段都必须基于已有信息分析原由、合理推断，鼓励多样性和有新意的内容，不许直接套用示例内容！！
    - 请尽可能添加一些事件、人物特质的细节，以及符合世界的真实与多样。角色卡片后续会用以角色扮演，来生成对话内容。
    - 每个字段内条目的多少可以自由决定，不要根据示例内条目数量。
    - 避免“模板”、“空洞”描述，必须具备社会和心理的可信度与细节感。
    - 输出仅为JSON，不添加任何额外解释、空字段或代码块。如果新增字段信息在已有数据不可推断，则需结合常识填补丰富该角色。
'''