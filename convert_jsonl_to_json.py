#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSONL to JSON Converter Script
将不规范的.jsonl文件转换为标准的JSON格式

支持的输入格式：
1. 连续的JSON对象（没有换行分隔）
2. 标准的JSONL格式（每行一个JSON对象）
3. 混合格式

输出格式：
- 标准的JSON数组格式，包含所有解析出的JSON对象
"""

import json
import re
import argparse
import sys
from pathlib import Path
from typing import List, Dict, Any


def extract_json_objects(content: str) -> List[Dict[Any, Any]]:
    """
    从文本内容中提取所有JSON对象
    
    Args:
        content: 输入的文本内容
        
    Returns:
        解析出的JSON对象列表
    """
    json_objects = []
    
    # 方法1: 尝试按行分割（标准JSONL格式）
    lines = content.strip().split('\n')
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        try:
            obj = json.loads(line)
            json_objects.append(obj)
            continue
        except json.JSONDecodeError:
            pass
    
    # 如果按行解析成功，返回结果
    if json_objects:
        return json_objects
    
    # 方法2: 处理连续的JSON对象（没有换行分隔）
    # 使用正则表达式找到所有可能的JSON对象边界
    brace_count = 0
    start_pos = 0
    i = 0
    
    while i < len(content):
        char = content[i]
        
        if char == '{':
            if brace_count == 0:
                start_pos = i
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                # 找到一个完整的JSON对象
                json_str = content[start_pos:i+1]
                try:
                    obj = json.loads(json_str)
                    json_objects.append(obj)
                except json.JSONDecodeError as e:
                    print(f"警告: 解析JSON对象时出错 (位置 {start_pos}-{i+1}): {e}")
                    print(f"问题内容: {json_str[:100]}...")
        
        i += 1
    
    return json_objects


def validate_json_objects(json_objects: List[Dict[Any, Any]]) -> List[Dict[Any, Any]]:
    """
    验证和清理JSON对象
    
    Args:
        json_objects: 待验证的JSON对象列表
        
    Returns:
        验证后的JSON对象列表
    """
    valid_objects = []
    
    for i, obj in enumerate(json_objects):
        if not isinstance(obj, dict):
            print(f"警告: 对象 {i+1} 不是字典类型，跳过")
            continue
            
        # 检查是否有基本的必需字段（根据你的数据结构调整）
        required_fields = ['Gender', 'Age', 'Occupation']
        missing_fields = [field for field in required_fields if field not in obj]
        
        if missing_fields:
            print(f"警告: 对象 {i+1} 缺少必需字段: {missing_fields}")
        
        valid_objects.append(obj)
    
    return valid_objects


def convert_jsonl_to_json(input_file: str, output_file: str = None, indent: int = 2) -> bool:
    """
    将JSONL文件转换为JSON文件
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径（如果为None，则自动生成）
        indent: JSON格式化缩进
        
    Returns:
        转换是否成功
    """
    input_path = Path(input_file)
    
    if not input_path.exists():
        print(f"错误: 输入文件不存在: {input_file}")
        return False
    
    # 自动生成输出文件名
    if output_file is None:
        output_file = input_path.with_suffix('.json').name
    
    output_path = Path(output_file)
    
    try:
        # 读取输入文件
        print(f"正在读取文件: {input_file}")
        with open(input_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取JSON对象
        print("正在解析JSON对象...")
        json_objects = extract_json_objects(content)
        
        if not json_objects:
            print("错误: 未找到有效的JSON对象")
            return False
        
        print(f"找到 {len(json_objects)} 个JSON对象")
        
        # 验证JSON对象
        print("正在验证JSON对象...")
        valid_objects = validate_json_objects(json_objects)
        
        if len(valid_objects) != len(json_objects):
            print(f"警告: {len(json_objects) - len(valid_objects)} 个对象验证失败")
        
        # 写入输出文件
        print(f"正在写入文件: {output_file}")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(valid_objects, f, ensure_ascii=False, indent=indent)
        
        print(f"转换完成! 输出文件: {output_file}")
        print(f"成功转换 {len(valid_objects)} 个对象")
        
        return True
        
    except Exception as e:
        print(f"错误: 转换过程中出现异常: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="将不规范的JSONL文件转换为标准的JSON格式",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python convert_jsonl_to_json.py input.jsonl
  python convert_jsonl_to_json.py input.jsonl -o output.json
  python convert_jsonl_to_json.py input.jsonl --indent 4
        """
    )
    
    parser.add_argument('input_file', help='输入的JSONL文件路径')
    parser.add_argument('-o', '--output', help='输出的JSON文件路径（默认自动生成）')
    parser.add_argument('--indent', type=int, default=2, help='JSON格式化缩进（默认2）')
    
    args = parser.parse_args()
    
    success = convert_jsonl_to_json(args.input_file, args.output, args.indent)
    
    if not success:
        sys.exit(1)


if __name__ == '__main__':
    main()
