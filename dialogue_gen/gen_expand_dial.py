#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import sys
import argparse
from typing import Optional, Literal, Dict, List
from pathlib import Path
import logging
from tqdm import tqdm
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

# 导入SynEmohaaData2025目录下的模块
sys.path.append(str(Path(__file__).resolve().parent))
from SynEmohaaData2025.gen_dial import (
    gen_dialogue, SimulatorSpec, AssistantSpec, 
    DifyUserSimulator, OpenAIUserSimulator, 
    DifySession, OpenAISession
)
from SynEmohaaData2025.user_simulator import ChenZhuangUserSimulationHelper

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('gen_expand_dial.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def process_single_dialogue(
    simulator_spec: SimulatorSpec,
    assistant_spec: AssistantSpec,
    user_profile_index: int,
    num_turns: int,
    output_dir: Path
):
    """处理单个对话"""
    try:
        # 创建用户模拟器
        helper = ChenZhuangUserSimulationHelper.load(str(simulator_spec.user_profile_filepath))
        simulator_session = next(helper.iterate_user_simulator(
            simulator_session_arguments=dict(
                base_url=simulator_spec.base_url,
                api_key=simulator_spec.api_key,
                user=f"user_{user_profile_index}"
            ),
            num_simulator=1
        ))
        simulator_session.initialize_user_profile(helper.get_user_profile(user_profile_index))
        
        # 创建心理咨询师会话
        assistant_session = assistant_spec.create_assistant_session()
        
        # 生成对话
        dialogue_data = gen_dialogue(
            simulator_session=simulator_session,
            assistant_session=assistant_session,
            num_turns=num_turns,
            return_internal_assistant_data=True,
            return_internal_user_data=True,
            dialogue_index=user_profile_index
        )
        
        # 获取角色信息，用于文件名
        role_info = helper.get_user_profile(user_profile_index).get("role_card", {})
        persona_id = role_info.get("persona_id", f"unknown_{user_profile_index}")
        
        # 保存对话
        output_file = output_dir / f"dialogue_{persona_id}.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(dialogue_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"对话 {user_profile_index} (persona_id: {persona_id}) 生成完成，已保存到 {output_file}")
        return True, user_profile_index, persona_id
    
    except Exception as e:
        logger.error(f"处理对话 {user_profile_index} 失败: {e}", exc_info=True)
        return False, user_profile_index, None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='使用扩展角色卡片生成对话')
    parser.add_argument('--expanded_json', default='personas_expand.json', 
                        help='扩展后的角色卡片JSON文件路径')
    parser.add_argument('--output_dir', default='generated_dialogues',
                        help='生成对话的输出目录')
    parser.add_argument('--num_turns', type=int, default=20,
                        help='每个对话的最大轮次')
    parser.add_argument('--n_thread', type=int, default=1,
                        help='并发线程数')
    parser.add_argument('--start_index', type=int, default=0,
                        help='开始处理的角色索引')
    parser.add_argument('--end_index', type=int, default=None,
                        help='结束处理的角色索引（不包含）')
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # 加载扩展后的角色卡片
    try:
        with open(args.expanded_json, 'r', encoding='utf-8') as f:
            expanded_personas = json.load(f)
        logger.info(f"成功加载 {len(expanded_personas)} 个扩展角色卡片")
    except Exception as e:
        logger.error(f"加载扩展角色卡片失败: {e}")
        return
    
    # 创建临时personas.json文件（与gen_dial.py兼容）
    temp_personas_path = Path("temp_personas.json")
    with open(temp_personas_path, 'w', encoding='utf-8') as f:
        json.dump(expanded_personas, f, ensure_ascii=False, indent=2)
    
    # 配置用户模拟器
    simulator_spec = SimulatorSpec(
        name="expanded_personas",
        user_profile_filepath=str(temp_personas_path),
        base_url=os.getenv("SIMULATOR_0614_BASE_URL", "http://43.153.114.96:3000/v1"),
        api_key=os.getenv("SIMULATOR_0614_API_KEY", "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"),
        impl="CZ"
    )
    
    # 配置心理咨询师
    assistant_spec = AssistantSpec(
        name="emohaa_0526",
        base_url=os.getenv("EMOHAA_0526_BASE_URL", "http://43.153.114.96:3000/v1"),
        api_key=os.getenv("EMOHAA_0526_API_KEY", "sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw"),
        backend="dify"
    )
    
    # 确定处理的角色范围
    end_index = args.end_index if args.end_index is not None else len(expanded_personas)
    profile_indices = list(range(args.start_index, min(end_index, len(expanded_personas))))
    
    if not profile_indices:
        logger.warning("没有要处理的角色")
        return
    
    logger.info(f"将处理角色索引 {args.start_index} 到 {end_index-1}，共 {len(profile_indices)} 个")
    
    # 单线程或多线程处理
    results = []
    if args.n_thread <= 1:
        # 单线程处理
        for idx in tqdm(profile_indices, desc="生成对话"):
            result = process_single_dialogue(
                simulator_spec=simulator_spec,
                assistant_spec=assistant_spec,
                user_profile_index=idx,
                num_turns=args.num_turns,
                output_dir=output_dir
            )
            results.append(result)
            # 添加延迟，避免API速率限制
            time.sleep(1)
    else:
        # 多线程处理
        with ThreadPoolExecutor(max_workers=args.n_thread) as executor:
            futures = [
                executor.submit(
                    process_single_dialogue,
                    simulator_spec=simulator_spec,
                    assistant_spec=assistant_spec,
                    user_profile_index=idx,
                    num_turns=args.num_turns,
                    output_dir=output_dir
                )
                for idx in profile_indices
            ]
            
            for future in tqdm(futures, desc="生成对话"):
                results.append(future.result())
    
    # 统计结果
    success_count = sum(1 for success, _, _ in results if success)
    logger.info(f"对话生成完成。成功: {success_count}/{len(profile_indices)}")
    
    # 清理临时文件
    if temp_personas_path.exists():
        temp_personas_path.unlink()

if __name__ == "__main__":
    main()