#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import sys
import argparse
from pathlib import Path
import logging
import time
from typing import Dict, List, Any, Optional, Literal, Generator
from tqdm import tqdm
from openai import OpenAI
import datetime
import random
import requests
from copy import deepcopy

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('simple_gen_dial.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 解析SSE响应的函数
def parse_sse(stream):
    """解析Server-Sent Events流"""
    for line in stream:
        if not line.strip():
            continue
        if isinstance(line, bytes):
            line = line.decode("utf-8")
        
        if line.startswith("data:"):
            obj = json.loads(line[5:].strip())
            yield obj

def request_dify_chatflow(
    base_url: str,
    api_key: str,
    query: str,
    *,
    conversation_id: str = "",
    inputs: Optional[Dict[str, Any]] = None,
    user: str,
    response_mode: Literal["streaming", "blocking"],
    timeout: Optional[int] = 180
):
    """请求Dify Chatflow API"""
    assert response_mode in ("streaming", "blocking")
    url = base_url + "/chat-messages"
    
    headers = {
        "Authorization": f"Bearer {api_key}"
    }
    
    body = {
        "inputs": inputs if inputs is not None else {},
        "query": query,
        "response_mode": response_mode,
        "conversation_id": conversation_id,
        "user": user,
        "files": []
    }
    
    response = requests.post(
        url=url,
        json=body,
        headers=headers,
        stream=(response_mode == "streaming"),
        timeout=timeout
    )
    response.raise_for_status()
    
    if response_mode == "blocking":
        return response.json()
    else:
        return parse_sse(
            response.iter_lines()
        )

class DifyClient:
    """Dify API客户端，用于调用emohaa咨询师"""
    
    def __init__(self, base_url: str, api_key: str, user: str, timeout=180):
        self.base_url = base_url
        self.api_key = api_key
        self.user = user
        self.conversation_id = ""
        self.dialogue = []
        self.inputs = None
        self.timeout = timeout
    
    def initialize(self, inputs: Optional[Dict[str, Any]] = None):
        """初始化对话历史"""
        self.conversation_id = ""
        self.dialogue = []
        self.inputs = inputs
        print("\n===== 咨询师初始化 =====")
        print(f"base_url: {self.base_url}")
        print(f"user: {self.user}")
        return self
    
    def chat(self, query: str, inputs: Optional[Dict[str, Any]] = None) -> str:
        """发送消息并获取响应"""
        if inputs is None and self.inputs is not None:
            inputs = self.inputs
        
        print("\n===== 咨询师输入 =====")
        print(f"query: {query}")
        print(f"conversation_id: {self.conversation_id}")
        print(f"历史对话: {self.dialogue}")
        
        response = request_dify_chatflow(
            base_url=self.base_url,
            api_key=self.api_key,
            query=query,
            conversation_id=self.conversation_id,
            inputs=inputs,
            user=self.user,
            response_mode="blocking",
            timeout=self.timeout
        )
        
        conversation_id = response['conversation_id']
        answer = response["answer"]
        
        print("\n===== 咨询师输出 =====")
        print(f"conversation_id: {conversation_id}")
        print(f"answer: {answer}")
        
        if self.conversation_id:
            assert self.conversation_id == conversation_id
        self.conversation_id = conversation_id
        self.dialogue.append((query, answer))
        
        return answer

class OpenAIClient:
    """OpenAI API客户端，用于模拟用户"""
    
    def __init__(self, base_url: str, api_key: str, model: str, temperature=0.7):
        self.client = OpenAI(
            base_url=base_url,
            api_key=api_key
        )
        self.model = model
        self.temperature = temperature
        self.history = []
    
    def initialize(self, system_prompt: str):
        """初始化对话历史"""
        self.history = [{"role": "system", "content": system_prompt}]
        print("\n===== 用户系统提示 =====")
        print(f"system_prompt: {system_prompt}")
        return self
    
    def chat(self, message: str) -> str:
        """发送消息并获取响应"""
        self.history.append({"role": "user", "content": message})
        
        print("\n===== 用户输入 =====")
        print(f"当前消息: {message}")
        print("历史记录:")
        for i, msg in enumerate(self.history):
            print(f"{i}. {msg['role']}: {msg['content'][:100]}..." if len(msg['content']) > 100 else f"{i}. {msg['role']}: {msg['content']}")
        
        response = self.client.chat.completions.create(
            messages=self.history,
            model=self.model,
            temperature=self.temperature,
            stream=False
        )
        
        assistant_message = response.choices[0].message.content
        self.history.append({"role": "assistant", "content": assistant_message})
        
        print("\n===== 用户输出 =====")
        print(f"response: {assistant_message}")
        
        return assistant_message

def generate_user_system_prompt(persona: Dict[str, Any]) -> str:
    """根据角色卡片生成用户的系统提示"""
    # 生成提示词
    prompt = f"""你现在扮演一个来寻求心理咨询的来访者，请完全按照下面的角色卡片来塑造自己，你的行为举止、情感表达方式、对咨询师的回应以及描述的问题都需要符合角色设定。
    
    注意：你不需要把所有细节都在第一次对话中说出来，可以像真实的心理咨询来访者一样，随着咨询的进行，逐渐展开。大约会聊20轮左右，可以适当展开，进行分配。
    不需要加括号展现肢体动作，你完全通过文字表达一切即可！！！

你的角色卡片：
- 性别: {persona.get('Gender', '未知')}
- 年龄: {persona.get('Age', '未知')}
- 职业: {persona.get('Occupation', '未知')}
- 主题: {persona.get('Topic', '未知')}
- 子主题: {persona.get('Subtopic', '未知')}
- 情境: {persona.get('Situation', '未知')}
- 性格特点: {', '.join(persona.get('Personality', []))}
- 情绪体验: {', '.join(persona.get('Emotional Experience Words', []))}
- 核心驱动力: {persona.get('Core Drive', '未知')}
- 反应模式: {persona.get('Reaction Pattern', '未知')}
- 过往事件: {persona.get('Event Description', '未知')}
- 应对策略与效果: {persona.get('Coped Strategies and Effects', '未知')}
- 目标与期望: {persona.get('Goals and Expectations', '未知')}
"""
    
    # 添加扩展字段（如果有）
    if 'StrengthsAndResources' in persona:
        strengths = '\n'.join(f"- {s}" for s in persona['StrengthsAndResources'])
        prompt += f"\n你的优势与资源:\n{strengths}\n"
    
    if 'SocialSupportSystem' in persona:
        prompt += "\n你的社会支持系统:\n"
        for person, desc in persona['SocialSupportSystem'].items():
            prompt += f"- {person}: {desc}\n"
    
    if 'FormativeExperiences' in persona:
        prompt += "\n你的重要过往经历:\n"
        for exp in persona['FormativeExperiences']:
            prompt += f"- 事件: {exp.get('事件', '')}\n"
            prompt += f"  影响: {exp.get('影响', '')}\n"
    
    if 'InterestsAndValues' in persona:
        prompt += "\n你的兴趣与价值观:\n"
        if 'Interests' in persona.get('InterestsAndValues', {}):
            prompt += f"- 兴趣: {', '.join(persona['InterestsAndValues']['Interests'])}\n"
        if 'Values' in persona.get('InterestsAndValues', {}):
            prompt += f"- 价值观: {', '.join(persona['InterestsAndValues']['Values'])}\n"

    # 添加额外指导
    prompt += """
在对话中，请记住以下要点：
1. 你是来寻求帮助的，但未必会一开始就直接表达你的真实问题，可能会有隐藏、回避或抗拒
2. 根据你的反应模式，你的表达方式可能有特定模式，请遵循这些模式
3. 你可能不擅长表达自己的情感，或者会用特定的方式表达
4. 你的回答应该符合你的年龄、性别和背景
5. 你不会提前知道心理咨询师会说什么，只能基于当前的对话进行回应
6. 不要表现得像一个AI助手，而是像一个有问题的来访者
7. 最重要的一点，不需要加括号展现肢体动作，你完全通过文字表达一切即可

你的第一句话应该是向咨询师打招呼，并简单提出你的困扰，但不要过于详细。
"""
    
    return prompt

def generate_and_save_dialogue(
    user_client: OpenAIClient, 
    counselor_client: DifyClient, 
    num_turns: int,
    output_file: Path,
    persona: Dict[str, Any],
    persona_id: str,
    user_model: str
) -> bool:
    """生成并保存对话，每轮对话后都保存一次"""
    dialogue = []
    metadata = {
        "persona_id": persona_id,
        "gender": persona.get("Gender", ""),
        "age": persona.get("Age", ""),
        "occupation": persona.get("Occupation", ""),
        "topic": persona.get("Topic", ""),
        "subtopic": persona.get("Subtopic", ""),
        "situation": persona.get("Situation", ""),
        "timestamp": datetime.datetime.now().isoformat(),
        "num_turns": num_turns,
        "user_model": user_model,
        "counselor_model": "emohaa_0526"
    }
    
    # 初始输出结构
    output_data = {
        "metadata": metadata,
        "dialogue": dialogue,
        "persona": persona
    }
    
    # 咨询师先说
    counselor_message = "你好，欢迎来到咨询。我是你的咨询师，请问今天有什么我可以帮助你的吗？"
    dialogue.append({"role": "counselor", "content": counselor_message})
    
    print("\n===== 对话开始 =====")
    print(f"角色ID: {persona_id}")
    print(f"初始咨询师消息: {counselor_message}")
    
    # 立即保存初始状态
    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    # 对话循环
    for turn in range(num_turns):
        try:
            print(f"\n===== 第 {turn+1}/{num_turns} 轮对话 =====")
            
            # 用户回应
            user_message = user_client.chat(counselor_message)
            dialogue.append({"role": "user", "content": user_message})
            
            # 立即保存用户回应
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            # 咨询师回应
            counselor_message = counselor_client.chat(user_message)
            dialogue.append({"role": "counselor", "content": counselor_message})
            
            # 立即保存咨询师回应
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"角色 {persona_id} 第 {turn+1} 轮对话完成")
            
        except Exception as e:
            logger.error(f"角色 {persona_id} 第 {turn+1} 轮对话失败: {e}")
            # 即使出错也保存已生成的对话
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            return False
    
    print("\n===== 对话结束 =====")
    return True

def process_single_persona(
    persona: Dict[str, Any],
    persona_index: int,
    api_base_url: str,
    api_key: str,
    emohaa_base_url: str,
    emohaa_api_key: str,
    user_model: str,
    num_turns: int,
    output_dir: Path
) -> bool:
    """处理单个角色对话"""
    try:
        persona_id = persona.get("persona_id", f"unknown_{persona_index}")
        logger.info(f"开始处理角色 {persona_index}: {persona_id}")
        
        # 创建用户客户端
        user_client = OpenAIClient(
            base_url=api_base_url,
            api_key=api_key,
            model=user_model,
            temperature=random.uniform(0.6, 0.8)  # 随机温度，增加多样性
        ).initialize(generate_user_system_prompt(persona))
        
        # 创建咨询师客户端 (使用Dify)
        counselor_client = DifyClient(
            base_url=emohaa_base_url,
            api_key=emohaa_api_key,
            user=f"counselor_{persona_index}"
        ).initialize()
        
        # 创建输出文件路径
        output_file = output_dir / f"dialogue_{persona_id}.json"
        
        # 生成并保存对话
        success = generate_and_save_dialogue(
            user_client=user_client,
            counselor_client=counselor_client,
            num_turns=num_turns,
            output_file=output_file,
            persona=persona,
            persona_id=persona_id,
            user_model=user_model
        )
        
        if success:
            logger.info(f"角色 {persona_id} 对话生成成功，已保存到 {output_file}")
        else:
            logger.warning(f"角色 {persona_id} 对话生成过程中出现错误，部分对话已保存到 {output_file}")
        
        return success
    
    except Exception as e:
        logger.error(f"处理角色 {persona_index} 失败: {e}", exc_info=True)
        return False

def load_personas(file_path: str) -> List[Dict[str, Any]]:
    """加载角色卡片，兼容原始和扩展两种格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            personas = json.load(f)
        
        # 确保每个角色卡都有persona_id
        for i, persona in enumerate(personas):
            if 'persona_id' not in persona:
                # 为没有persona_id的角色卡生成一个唯一ID
                persona['persona_id'] = f"auto_generated_id_{i}"
        
        return personas
    except Exception as e:
        logger.error(f"加载角色卡片文件失败: {e}")
        return []

def find_persona_by_id(personas: List[Dict[str, Any]], persona_id: str) -> Optional[Dict[str, Any]]:
    """根据persona_id查找角色卡片"""
    for persona in personas:
        if persona.get('persona_id') == persona_id:
            return persona
    return None

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='使用角色卡片生成对话')
    parser.add_argument('--personas_file', default='personas_expand.json', 
                        help='角色卡片JSON文件路径，支持原始和扩展两种格式')
    parser.add_argument('--output_dir', default='ori_simple_dialogues',
                        help='生成对话的输出目录')
    parser.add_argument('--num_turns', type=int, default=20,
                        help='每个对话的最大轮次（单向，实际总轮次为2倍）')
    parser.add_argument('--api_base_url', default='http://*************:3000/v1',
                        help='OpenAI API基础URL')
    parser.add_argument('--api_key', 
                        default='sk-RMerNTL9uP4lmHh_1rwUHJvcaqKXXzb7IrAtjWTa5Ln_aFMFTNPkuzIi7vw',
                        help='OpenAI API Key')
    parser.add_argument('--emohaa_base_url', 
                        default=os.getenv("EMOHAA_0526_BASE_URL", "http://*************/v1"),
                        help='Emohaa咨询师API基础URL')
    parser.add_argument('--emohaa_api_key', 
                        default=os.getenv("EMOHAA_0526_API_KEY", "app-Q19whS1F0MNyHUPrDZ7vGIDJ"),
                        help='Emohaa咨询师API Key')
    parser.add_argument('--user_model', default='deepseek-r1-250528',
                        help='用户角色使用的模型')
    parser.add_argument('--start_index', type=int, default=0,
                        help='开始处理的角色索引')
    parser.add_argument('--end_index', type=int, default=None,
                        help='结束处理的角色索引（不包含）')
    parser.add_argument('--persona_id', 
                        help='指定要处理的角色ID，如果提供则忽略start_index和end_index')
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True, parents=True)
    
    # 加载角色卡片
    personas = load_personas(args.personas_file)
    if not personas:
        logger.error(f"未能从文件 {args.personas_file} 加载到有效的角色卡片")
        return
    
    logger.info(f"成功加载 {len(personas)} 个角色卡片")
    
    # 确定要处理的角色
    personas_to_process = []
    
    if args.persona_id:
        # 根据persona_id选择角色
        persona = find_persona_by_id(personas, args.persona_id)
        if persona:
            personas_to_process = [persona]
            logger.info(f"找到指定的角色ID: {args.persona_id}")
        else:
            logger.error(f"未找到指定的角色ID: {args.persona_id}")
            return
    else:
        # 根据索引范围选择角色
        end_index = args.end_index if args.end_index is not None else len(personas)
        personas_to_process = personas[args.start_index:end_index]
        
        if not personas_to_process:
            logger.warning("没有要处理的角色")
            return
        
        logger.info(f"将处理角色索引 {args.start_index} 到 {end_index-1}，共 {len(personas_to_process)} 个")
    
    # 处理每个角色
    success_count = 0
    for i, persona in enumerate(tqdm(personas_to_process, desc="生成对话")):
        persona_index = args.start_index + i if not args.persona_id else i
        success = process_single_persona(
            persona=persona,
            persona_index=persona_index,
            api_base_url=args.api_base_url,
            api_key=args.api_key,
            emohaa_base_url=args.emohaa_base_url,
            emohaa_api_key=args.emohaa_api_key,
            user_model=args.user_model,
            num_turns=args.num_turns,
            output_dir=output_dir
        )
        
        if success:
            success_count += 1
        
        # 添加延迟，避免API速率限制
        time.sleep(1)
    
    logger.info(f"对话生成完成。成功: {success_count}/{len(personas_to_process)}")

if __name__ == "__main__":
    main()